# Project Rules and Guidelines for `vocab`

## Architecture Overview

This project follows a layered architecture pattern:

-   **API Layer** (`src/backend/api`): Authentication, input validation, and orchestration
-   **Service Layer** (`src/backend/services`): Business logic and service orchestration
-   **Repository Layer** (`src/backend/repositories`): Data access and Prisma interactions
-   **Wire Layer** (`src/backend/wire.ts`): Dependency injection and service wiring
-   **Models** (`src/models`): Extended types with business logic
-   **Components** (`src/components`): Reusable UI components
-   **Hooks** (`src/hooks`): Custom React hooks for state management
-   **Lib** (`src/lib`): Utilities, auth, translations, and shared logic

---

# Database Schema Overview

This project uses PostgreSQL with Prisma ORM. The database schema is defined in `prisma/schema.prisma`.

## Database Configuration

-   **Provider**: PostgreSQL
-   **ORM**: Prisma Client
-   **Output**: `../node_modules/.prisma/client`

## Enums

### Language

-   `EN`: English
-   `VI`: Vietnamese

### Provider

-   `TELEGRAM`: Telegram authentication provider

### PartsOfSpeech

-   `NOUN`, `VERB`, `ADJECTIVE`, `ADVERB`, `PRONOUN`, `PREPOSITION`, `CONJUNCTION`, `INTERJECTION`

### Difficulty

-   `BEGINNER`, `INTERMEDIATE`, `ADVANCED`

### Length

-   `SHORT`, `MEDIUM`, `LONG`

## Core Models

### User

-   **Primary Key**: `id` (UUID)
-   **Authentication**: `provider` + `provider_id` (unique combination)
-   **Status**: `disabled` flag for account management
-   **Relations**: Collections, LastSeenWords, Keywords, Feedbacks
-   **Timestamps**: `created_at`, `updated_at`

### Word

-   **Primary Key**: `id` (UUID)
-   **Unique Constraint**: `term` + `language` combination
-   **Content**: `term`, `language`, optional `audio_url`
-   **Relations**: Multiple definitions
-   **Timestamps**: `created_at`, `updated_at`

### Definition

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `word_id` references Word
-   **Content**: `pos` (parts of speech array), `ipa` (pronunciation), `images` array
-   **Relations**: Multiple explains and examples

### Explain & Example

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `definition_id` references Definition
-   **Bilingual Content**: `EN` and `VI` fields for both languages

### Collection

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `user_id` references User
-   **Languages**: `target_language` (default EN), `source_language` (default VI)
-   **Content Arrays**: `word_ids`, `paragraph_ids`, `keyword_ids`
-   **Settings**: `enable_learn_word_notification` flag
-   **Timestamps**: `created_at`, `updated_at`
-   **Index**: `user_id` for performance

### Keyword

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `user_id` references User
-   **Content**: `content` string
-   **Index**: `user_id` for performance

### LastSeenWord

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `user_id` references User
-   **Unique Constraint**: `user_id` + `word_id` combination
-   **Tracking**: `last_seen_at`, `review_count`
-   **Reference**: `word_id` (string, not foreign key)
-   **Index**: `user_id` for performance

### Paragraph

-   **Primary Key**: `id` (UUID)
-   **Content**: `content` string
-   **Metadata**: `difficulty`, `language`, `length`
-   **Relations**: Multiple choice exercises
-   **Indexes**: `language`, `difficulty` for filtering

### MultipleChoiceExercise

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `paragraph_id` references Paragraph
-   **Content**: `question`, `options` array, `answer` index, optional `explanation`
-   **Index**: `paragraph_id` for performance

### Feedback

-   **Primary Key**: `id` (UUID)
-   **Foreign Key**: `user_id` references User
-   **Content**: `message` string
-   **Timestamp**: `created_at`
-   **Index**: `user_id` for performance

## Database Design Principles

-   **UUID Primary Keys**: All models use UUID for primary keys
-   **Soft References**: Some relations use string IDs instead of foreign keys for flexibility
-   **Bilingual Support**: Content models support both English and Vietnamese
-   **Performance Indexes**: Strategic indexing on frequently queried fields
-   **Unique Constraints**: Prevent duplicate data (e.g., same word in same language)
-   **Timestamps**: Automatic tracking of creation and update times
-   **Array Fields**: PostgreSQL arrays for storing multiple related IDs

## Migration Guidelines

-   All schema changes must be done through Prisma migrations
-   Migration files are stored in `prisma/migrations/`
-   Always test migrations in development before applying to production
-   Use descriptive migration names that explain the change

## Database Update Guidelines through Prisma

### Schema Changes

-   **Always modify `prisma/schema.prisma` first** before generating migrations
-   **Never edit migration files manually** - let Prisma generate them
-   **Use descriptive names** for migrations that explain the change purpose
-   **Test schema changes locally** before applying to production

### Migration Commands

```bash
# Generate a new migration after schema changes
npx prisma migrate dev --name descriptive_migration_name

# Apply pending migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma Client after schema changes
npx prisma generate
```

### Data Seeding

-   **Seed scripts** should be placed in `prisma/seed.ts` or `prisma/seed.js`
-   **Run seeding** with `npx prisma db seed`
-   **Seed data should be idempotent** - safe to run multiple times
-   **Use upsert operations** for seed data to avoid duplicates

### Database Inspection

```bash
# Open Prisma Studio for database inspection
npx prisma studio

# View current database schema
npx prisma db pull

# Validate schema against database
npx prisma validate
```

### Best Practices for Database Updates

-   **Backup production data** before applying migrations
-   **Use transactions** for complex data migrations
-   **Add indexes carefully** - they can lock tables during creation
-   **Consider downtime** for large table alterations
-   **Test migrations** on a copy of production data first
-   **Use `@@map` and `@map`** to maintain existing table/column names when refactoring
-   **Add new columns as optional first**, then make required in separate migration if needed
-   **Use `@default` values** for new required columns to avoid migration issues

### Migration Safety Checklist

1. **Schema validation**: Run `npx prisma validate` after changes
2. **Local testing**: Test migration on local development database
3. **Staging deployment**: Apply migration to staging environment first
4. **Data backup**: Ensure production backup exists before deployment
5. **Rollback plan**: Have a rollback strategy for critical migrations
6. **Monitor performance**: Watch for performance impacts after deployment

### Common Migration Patterns

```prisma
// Adding a new optional column
model User {
  id        String   @id @default(uuid())
  email     String   @unique
  newField  String?  // Optional first
  createdAt DateTime @default(now())
}

// Adding a new required column with default
model User {
  id        String   @id @default(uuid())
  email     String   @unique
  newField  String   @default("default_value")
  createdAt DateTime @default(now())
}

// Renaming a column (use @map to maintain DB column name)
model User {
  id           String   @id @default(uuid())
  emailAddress String   @unique @map("email")
  createdAt    DateTime @default(now())
}
```

### Environment-Specific Considerations

-   **Development**: Use `prisma migrate dev` for iterative development
-   **Staging**: Use `prisma migrate deploy` to test production-like deployments
-   **Production**: Use `prisma migrate deploy` with proper backup and monitoring
-   **CI/CD**: Include `prisma generate` in build process
-   **Docker**: Ensure migrations run before application starts

---

# Rules for `vocab/src/backend/api`

## General Principles

-   All API modules in `src/backend/api` handle authentication, input validation, and orchestration only. They call services via dependency injection from `@/backend/wire`, never repositories directly.
-   All services in `src/backend/services` contain business logic and call repositories or other services, never Prisma directly.
-   All repositories in `src/backend/repositories` handle data access using Prisma, extending `BaseRepository` interface.
-   All new files must use dash-case naming convention (e.g., `collection.ts`, `user-settings.ts`).
-   Imports of local files must always use the `@/` alias (e.g., `import { getCollectionService } from '@/backend/wire'`).
-   All API endpoints must use `'use server';` at the top of the file.
-   All API functions must be exported as named async functions.
-   All API functions must validate input and throw descriptive errors for missing or invalid parameters.
-   All API functions that require authentication must call `auth()` from `@/lib` and extract user ID from session.
-   All API functions must handle and rethrow errors with meaningful messages, using custom error classes from `@/backend/errors`.
-   All API functions must have clear JSDoc comments describing parameters, return values, and possible errors.
-   All API modules must only export API functions, never types or constants.

## File Structure

-   Each feature (e.g., collection, feedback, keyword, paragraph, user, word) must have its own API file in `src/backend/api`.
-   The `index.ts` file in `src/backend/api` must export all API functions from the feature files.
-   API files must not import from other API files; only from application, services, models, types, or lib.

## Error Handling

-   Use custom error classes (`ValidationError`, `NotFoundError`, `UnauthorizedError`, etc.) from `@/backend/errors` for all error cases.
-   Never return raw errors to the client; always throw or rethrow with a clear message.

## Authentication

-   All API functions that require a user context must call `auth()` and extract the user ID from the session.
-   If authentication fails, throw `UnauthorizedError` or a plain `Error('Unauthorized')` as appropriate.

## Naming

-   API function names must follow the pattern: `{action}{Feature}Api` (e.g., `createCollectionApi`, `getUserByIdApi`).

## Example API Function Template

```ts
'use server';

import { ValidationError, UnauthorizedError, NotFoundError } from '@/backend/errors';
import { getFeatureService } from '@/backend/wire';
import { auth } from '@/lib';

/**
 * Description of what this API does.
 * @param param1 - Description
 * @param param2 - Description
 * @returns Description of return value
 * @throws {ValidationError} If input is invalid
 * @throws {UnauthorizedError} If user is not authenticated
 * @throws {NotFoundError} If resource is not found
 */
export async function someFeatureApi(param1: string, param2: number) {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) throw new UnauthorizedError('Unauthorized');

	if (!param1) {
		throw new ValidationError('Param1 is required');
	}

	const featureService = getFeatureService();
	try {
		const result = await featureService.performAction(userId, param1, param2);
		return result;
	} catch (error) {
		if (error instanceof NotFoundError) {
			throw error;
		}
		throw new Error(
			`Failed to perform action: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}
```

## Directory-Specific Rules

-   `src/backend/api/collection.ts`: Handles all collection CRUD and word/term management for collections.
-   `src/backend/api/feedback.ts`: Handles feedback submission.
-   `src/backend/api/keyword.ts`: Handles keyword CRUD for authenticated users.
-   `src/backend/api/last-seen-word.ts`: Handles saving last seen word for a user.
-   `src/backend/api/llm.ts`: Handles LLM-powered word/term/paragraph generation.
-   `src/backend/api/server-auth.ts`: Handles provider login and logout.
-   `src/backend/api/user.ts`: Handles user CRUD and settings.
-   `src/backend/api/word.ts`: Handles word search, retrieval, and review logic.

## Do Not

-   Do not put business logic in API files.
-   Do not call repositories or Prisma directly from API files.
-   Do not use default exports in API files.
-   Do not use snake_case or camelCase for file names; always use dash-case.
-   Do not import from other API files.

---

# Rules for `vocab/src/backend/services`

## General Principles

-   All service files must implement an interface and implementation class pattern.
-   Services contain business logic and orchestrate repositories or other services.
-   Services never call Prisma directly; they use repositories for data access.
-   Service implementations receive repository getters via dependency injection from the wire layer.
-   All service files must use dash-case naming (e.g., `collection.service.ts`).
-   Export both the interface and implementation class from each service file.
-   Services should enrich plain repository data into detailed models when needed.

## Naming Conventions

-   Service interfaces: `{Feature}Service` (e.g., `CollectionService`)
-   Service implementations: `{Feature}ServiceImpl` (e.g., `CollectionServiceImpl`)
-   Service files: `{feature}.service.ts` (e.g., `collection.service.ts`)

## File Structure

-   Each service file exports an interface and implementation class.
-   The `index.ts` file in `src/backend/services` must export all services.
-   Services may depend on other services through dependency injection.

## Available Services

-   `auth.service.ts`: Authentication and user session management
-   `cache.service.ts`: Caching operations and cache management
-   `collection.service.ts`: Collection CRUD and word/term management
-   `feedback.service.ts`: Feedback submission and retrieval
-   `keyword.service.ts`: Keyword management and operations
-   `last-seen-word.service.ts`: Tracking last seen words for users
-   `llm.service.ts`: Large Language Model integration for AI features
-   `user.service.ts`: User profile and settings management
-   `word.service.ts`: Word search, retrieval, and vocabulary operations

---

# Rules for `vocab/src/backend/repositories`

## General Principles

-   All repositories extend the `BaseRepository` interface from `base.repository.ts`.
-   Repository implementations extend `BaseRepositoryImpl` class.
-   Repositories handle only data access using Prisma models.
-   Repository files must use dash-case naming (e.g., `collection.repository.ts`).
-   Export both interface and implementation class from each repository file.

## Naming Conventions

-   Repository interfaces: `{Feature}Repository` (e.g., `CollectionRepository`)
-   Repository implementations: `{Feature}RepositoryImpl` (e.g., `CollectionRepositoryImpl`)
-   Repository files: `{feature}.repository.ts` (e.g., `collection.repository.ts`)

## Structure

-   All repositories implement CRUD operations through the base class.
-   Add feature-specific methods as needed in the interface and implementation.
-   Never include business logic in repositories.

---

# Rules for `vocab/src/backend/errors`

## General Principles

-   All custom errors extend `ApplicationError` from `application.errors.ts`.
-   Error classes must include a descriptive message and error code.
-   Use specific error types for different scenarios (ValidationError, NotFoundError, UnauthorizedError, etc.).
-   Export all error classes from `index.ts`.

## Available Error Classes

-   `ApplicationError`: Base error class with code and message
-   `ValidationError`: For input validation errors
-   `NotFoundError`: For resource not found errors
-   `UnauthorizedError`: For authentication/authorization errors
-   `HandlerNotFoundError`: For missing handlers in application layer

---

# Rules for `vocab/src/models`

## General Principles

-   Models extend Prisma types with additional computed properties or related data.
-   Model files must use dash-case naming when applicable.
-   Export all model types from `index.ts`.
-   Models represent enriched business entities, not just database records.

## Naming Conventions

-   Detailed models: `{Feature}WithDetail` (e.g., `CollectionWithDetail`)
-   Basic extended models: `{Feature}With{Property}` (e.g., `CollectionWithBasicWords`)

---

# Rules for `vocab/src/config`

## General Principles

-   Configuration files centralize environment variables and app settings.
-   Export configuration objects grouped by domain (server, auth, etc.).
-   Use TypeScript for type safety of configuration values.
-   Provide sensible defaults for development environment.

## Structure

-   `config.ts`: Main configuration file with domain-specific config objects
-   `index.ts`: Re-exports all configuration

---

# Rules for `vocab/src/contexts`

## General Principles

-   Context files must start with `'use client';`.
-   Use dash-case naming for context files (e.g., `collection-context.tsx`).
-   Export both the context provider component and custom hook.
-   Contexts should be focused on specific domains or features.

## Naming Conventions

-   Context files: `{feature}-context.tsx`
-   Context types: `{Feature}ContextType`
-   Provider components: `{Feature}Provider`
-   Custom hooks: `use{Feature}Context`

## Structure

-   Define context type interface
-   Create context with createContext
-   Export custom hook that validates context usage
-   Export provider component

---

# Rules for `vocab/src/types`

## General Principles

-   Types directory contains global type definitions and external library declarations.
-   Use `.d.ts` files for type declarations of external libraries.
-   Export all types from `index.ts`.

---

These rules must be followed for all code in the backend layers and related modules.

# Rules and Guidelines for `vocab/src/hooks`

## General Principles

-   All hook files must start with `'use client';`.
-   Hook filenames must use dash-case (e.g., `use-words.ts`, `use-collections.ts`).
-   Hook function names must use camelCase and start with `use` (e.g., `useWords`, `useCollections`).
-   Hooks must not contain complex business logic or direct database access; only call APIs or separated services.
-   No default exports; only named exports for functions or variables.
-   Hooks should return an object or tuple containing state, action functions, and loading/error status if applicable.
-   Use `useCallback` and `useMemo` to optimize performance and avoid unnecessary function recreation.
-   If there are multiple loading states for different actions, group them in an object or use clearly named variables.
-   Prefer creating a `setLoadingError` helper to synchronize loading and error states.
-   Do not call API endpoints outside the scope of the hook (e.g., do not call paragraph APIs in a collection hook).

## Error and Loading Handling

-   Always manage loading and error state for each async operation.
-   When starting an async operation, set loading = true and error = null.
-   When the operation finishes (success or failure), set loading = false.
-   On error, set error to an instance of Error with a clear message.

## Naming and Structure

-   Hook names must clearly reflect their purpose (e.g., `useWords`, `useCollections`, `useAuth`).
-   If a hook returns multiple action functions, group them in the returned object.
-   If there are sub-hooks (e.g., `useCollection` inside `use-collections.ts`), export them clearly and name them consistently.

## Import/Export

-   Always import APIs from `@/backend/api` or other clearly defined modules.
-   Do not cross-import between hooks unless absolutely necessary and justified.
-   The `index.ts` file should only be used to re-export hooks.

## Available Hooks

-   `use-auth.ts`: Authentication state management
-   `use-collections.ts`: Collection CRUD operations and state
-   `use-keywords.ts`: Keyword management
-   `use-last-seen-word.ts`: Last seen word tracking
-   `use-llm.ts`: LLM service interactions
-   `use-media-query.ts`: Responsive design utilities
-   `use-words.ts`: Word search and management
-   `telegram-login.ts`: Telegram authentication
-   `helpers/use-safe-state.ts`: Safe state management for mounted components

## Helper Hooks

-   Helper hooks in `src/hooks/helpers/` provide utility functionality for other hooks.
-   Helper hooks must be generic and reusable across different features.
-   Export helper hooks from the main `index.ts` file for easy access.

---

# Rules for `vocab/src/lib`

## General Principles

-   The `lib` directory contains utilities, auth, translations, and shared logic.
-   All utility functions should be pure functions when possible.
-   Export all utilities from `index.ts` for easy importing.
-   Use TypeScript for type safety across all lib functions.

## File Structure and Purpose

-   `auth.ts`: Authentication utilities and session management
-   `component-utils.ts`: Component helper utilities
-   `debounce.ts`: Debouncing utilities
-   `error-handling.ts`: Error handling utilities
-   `error-patterns.ts`: Error pattern matching
-   `indexed-db-utils.ts`: IndexedDB operations for offline functionality
-   `language.ts`: Language-related utilities
-   `query-provider.tsx`: React Query provider setup
-   `register-service-worker.ts`: Service worker registration
-   `text-diff.ts`: Text comparison utilities
-   `translation-context.tsx`: Translation context provider
-   `utils.ts`: General utility functions

## Translation System

-   All translation files must be in `src/lib/translations/`
-   Each translation file must import `TranslationDict` from `./translation-dict`
-   Translation keys should follow dot-separated convention: `feature.section.key`
-   Each translation key must have sub-keys for supported languages (EN, VI)
-   Export translation constants with descriptive names (e.g., `homeTranslations`, `collectionsTranslations`)

## Available Translation Files

-   `about.ts`, `accessibility.ts`, `collections.ts`, `common.ts`, `difficulty.ts`
-   `errors.ts`, `feedback.ts`, `grammar.ts`, `headings.ts`, `home.ts`
-   `keywords.ts`, `language.ts`, `legal.ts`, `lengths.ts`, `nav.ts`
-   `paragraphs.ts`, `qa-practice.ts`, `review.ts`, `search.ts`, `theme.ts`
-   `toast.ts`, `ui.ts`, `words.ts`

---

# Rules for `vocab/src/components`

## General Principles

-   All component files must use PascalCase naming for component functions.
-   Component files should use dash-case for filenames when they contain multiple words.
-   Components must start with `'use client';` if they use client-side React hooks or browser APIs.
-   Use Tailwind CSS for styling via `className` attributes.
-   Export components as named exports, not default exports.
-   Group related components in subdirectories.

## File Structure

-   `home/`: Components specific to the home page
-   `layout/`: Layout components (headers, navigation, auth components)
-   `ui/`: Reusable UI components and primitives
-   `loading-provider.tsx`: Global loading state provider

## Component Guidelines

-   Prefer composition over complex prop drilling
-   Use TypeScript interfaces for component props
-   Include proper accessibility attributes (aria-label, role, etc.)
-   Use framer-motion for animations when needed
-   Keep components focused on a single responsibility

---

# Rules for `vocab/src/app` (Next.js App Router)

## General Principles

-   Page files must be named `page.tsx` and located within their respective route segment folders under `src/app`.
-   Layout files must be named `layout.tsx` for shared layouts.
-   Page components should be React functional components.
-   Pages can be Server Components (default) or Client Components (with `'use client';`).
-   Use custom hooks from `src/hooks` for client-side state management.

## Naming and Structure

-   Page component functions should be named in `PascalCase` (e.g., `HomePage`, `CollectionsPage`).
-   Pages must be default exported: `export default function PageName() { ... }`
-   Route groups use parentheses: `(legal)/` for routes that don't affect URL structure

## Data Fetching

-   Prefer custom hooks (from `src/hooks`) for client-side state management in Client Components
-   For Server Components, fetch data directly within the component
-   Use Next.js caching mechanisms appropriately

## Available Routes

-   `/`: Home page (`page.tsx`)
-   `/collections/`: Collections management
-   `/(legal)/`: Legal pages (terms, privacy, etc.)

---

# Rules for `vocab/src/backend/middleware`

## General Principles

-   Middleware handles cross-cutting concerns like authentication, logging, and request validation.
-   All middleware files must use `'use server';` directive at the top.
-   All middleware functions should be composable and reusable.
-   Middleware should not contain business logic.
-   Use TypeScript for type safety in middleware functions.
-   Middleware files must use dash-case naming (e.g., `auth.middleware.ts`).

## Available Middleware

-   `auth.middleware.ts`: Handles authentication validation and user context injection

## Structure

-   Middleware functions should accept NextRequest and return NextResponse or middleware result
-   Use services from wire layer for business logic operations
-   Handle development vs production environments appropriately

---

# Rules for `vocab/src/backend/utils`

## General Principles

-   Backend utilities contain helper functions specific to server-side operations.
-   All utility functions should be pure functions when possible.
-   Export utilities with clear, descriptive names.
-   Do not mix client-side and server-side utilities.
-   Use dash-case naming for utility files (e.g., `token.util.ts`).

## Available Utilities

-   `token.util.ts`: JWT token generation and verification utilities
-   `index.ts`: Re-exports all utility functions

## Structure

-   Export all utilities from `index.ts` for easy importing
-   Group related utilities in separate files
-   Use TypeScript for type safety

---

# Rules for `vocab/src/middleware.ts` (Next.js Root Middleware)

## General Principles

-   The root middleware file handles Next.js request interception.
-   Must export a `middleware` function and `config` object.
-   Use path matchers to control which routes the middleware applies to.
-   Delegate specific middleware logic to dedicated files in `src/backend/middleware/`.

## Structure

-   Import specific middleware from `src/backend/middleware/`
-   Define public paths that bypass authentication
-   Handle different URL patterns appropriately
-   Use Next.js middleware config for path matching

---

# Rules for `vocab/src/backend/wire.ts`

## General Principles

-   The wire module handles dependency injection for services and repositories.
-   All service and repository instances should be created through the wire module.
-   Use getter functions to provide lazy initialization.
-   Maintain clear separation between different layers (repository, service, etc.).
-   Wire should export getter functions like `getCollectionService()`, `getUserRepository()`, etc.

## Structure

-   Import all repository and service classes
-   Create singleton instances
-   Export getter functions for each service and repository
-   Handle dependency injection between services

---

# Project Structure Summary

## Current Directory Structure

```
src/
├── middleware.ts                 # Next.js root middleware
├── app/                         # Next.js App Router pages
│   ├── layout.tsx              # Root layout
│   ├── page.tsx                # Home page
│   ├── globals.css             # Global styles
│   ├── (legal)/                # Legal pages route group
│   └── collections/            # Collections feature pages
├── backend/                     # Server-side code
│   ├── wire.ts                 # Dependency injection
│   ├── api/                    # API endpoints (9 files)
│   ├── services/               # Business logic (10 files)
│   ├── repositories/           # Data access (8 files)
│   ├── middleware/             # Server middleware (1 file)
│   ├── utils/                  # Backend utilities (2 files)
│   └── errors/                 # Custom error classes (2 files)
├── components/                  # React components
│   ├── loading-provider.tsx    # Global loading state
│   ├── home/                   # Home page components
│   ├── layout/                 # Layout components
│   └── ui/                     # Reusable UI components
├── hooks/                       # Custom React hooks (9 files)
│   └── helpers/                # Utility hooks (1 file)
├── lib/                        # Utilities and shared logic (11 files)
│   └── translations/           # Translation files (25 files)
├── models/                     # Extended type definitions (6 files)
├── contexts/                   # React contexts (1 file)
├── config/                     # Configuration files (2 files)
└── types/                      # Type definitions (2 files)
```

## Architecture Principles

1. **Layered Architecture**: Clear separation between API, Service, Repository, and Model layers
2. **Dependency Injection**: Services and repositories injected through `wire.ts`
3. **Type Safety**: TypeScript throughout with extended Prisma types
4. **Error Handling**: Custom error classes with specific error codes
5. **Internationalization**: Comprehensive translation system supporting EN/VI
6. **Component Organization**: Grouped by feature and reusability
7. **Hook Pattern**: Custom hooks for state management and API interactions

## Key Patterns

-   **Dash-case naming** for all files
-   **Interface + Implementation** pattern for services and repositories
-   **Named exports** (no default exports in most cases)
-   **`'use client'`** directive for client components
-   **`'use server'`** directive for API endpoints
-   **`@/` alias** for all local imports
-   **Error boundaries** with custom error types
-   **Translation keys** using dot notation

---

This comprehensive rule set ensures consistency, maintainability, and scalability across the entire vocab project codebase.
