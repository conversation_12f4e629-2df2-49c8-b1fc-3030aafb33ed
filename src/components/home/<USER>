'use client';

import { Button, KeyboardNavigation, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { FolderHeart } from 'lucide-react';
import Link from 'next/link';
import { containerVariants, cardVariants } from './animation-variants';

export function CollectionsSection() {
	const { t } = useTranslation();

	return (
		<KeyboardNavigation orientation="horizontal">
			<motion.div variants={containerVariants} className="grid grid-cols-1 gap-6">
				<motion.div variants={cardVariants}>
					<Link href="/collections" className="w-full block">
						<Button
							type="button"
							variant="outline"
							className="w-full h-auto py-6 flex flex-col items-center gap-3 whitespace-normal hover:scale-105 transition-transform duration-300 hover:bg-accent/50"
							size="lg"
							aria-label={t('home.collections')}
						>
							<FolderHeart className="size-8 text-primary" aria-hidden="true" />
							<div>
								<div className="font-semibold text-lg">
									<Translate text="home.collections" />
								</div>
								<div className="text-sm text-muted-foreground mt-1">
									<Translate text="home.group_vocabulary" />
								</div>
							</div>
						</Button>
					</Link>
				</motion.div>
			</motion.div>
		</KeyboardNavigation>
	);
}
