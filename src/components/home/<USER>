'use client';

import { Translate } from '@/components/ui';
import { motion } from 'framer-motion';
import { itemVariants } from './animation-variants';

export function HeroSection() {
	return (
		<motion.div variants={itemVariants} className="space-y-4">
			<motion.h1
				className="text-5xl md:text-6xl font-extrabold"
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
			>
				<Translate text="home.title" />
			</motion.h1>
			<motion.p
				className="text-muted-foreground text-lg max-w-2xl mx-auto"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.2 }}
			>
				<Translate text="home.subtitle" />
			</motion.p>
		</motion.div>
	);
}
