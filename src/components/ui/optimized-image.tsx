'use client';

import { cn } from '@/lib';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface OptimizedImageProps {
	src: string;
	alt: string;
	width?: number;
	height?: number;
	className?: string;
	quality?: number;
	priority?: boolean;
	fill?: boolean;
	sizes?: string;
	onLoad?: () => void;
}

export function OptimizedImage({
	src,
	alt,
	width,
	height,
	className,
	quality = 85,
	priority = false,
	fill = false,
	sizes,
	onLoad,
}: OptimizedImageProps) {
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(false);
	const [imageSrc, setImageSrc] = useState(src);

	// Reset state when src changes
	useEffect(() => {
		setLoading(true);
		setError(false);
		setImageSrc(src);
	}, [src]);

	return (
		<div
			className={cn(
				'relative overflow-hidden',
				loading && 'bg-gray-100 animate-pulse',
				error && 'bg-gray-200',
				className
			)}
		>
			{!error ? (
				<Image
					src={imageSrc}
					alt={alt}
					width={!fill ? width : undefined}
					height={!fill ? height : undefined}
					quality={quality}
					priority={priority}
					fill={fill}
					sizes={sizes || '(max-width: 768px) 100vw, 50vw'}
					className={cn(
						'transition-opacity duration-300',
						loading ? 'opacity-0' : 'opacity-100'
					)}
					onLoad={() => {
						setLoading(false);
						onLoad?.();
					}}
					onError={() => {
						setLoading(false);
						setError(true);
					}}
				/>
			) : (
				<div className="absolute inset-0 flex items-center justify-center bg-gray-200 text-gray-500 text-sm">
					Failed to load image
				</div>
			)}
		</div>
	);
}
