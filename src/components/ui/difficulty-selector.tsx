'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { Difficulty } from '@prisma/client';

interface DifficultySelectorProps {
	value?: Difficulty;
	onChange: (value: Difficulty) => void;
	className?: string;
}

export function DifficultySelector({
	value,
	onChange,
	className,
	disabled,
}: DifficultySelectorProps & { disabled?: boolean }) {
	const { t } = useTranslation();

	return (
		<Select value={value} onValueChange={onChange} disabled={disabled}>
			<SelectTrigger className={className}>
				<SelectValue placeholder={t('difficulty.select_difficulty')} />
			</SelectTrigger>
			<SelectContent>
				<SelectItem value={Difficulty.BEGINNER}>{t('difficulty.BEGINNER')}</SelectItem>
				<SelectItem value={Difficulty.INTERMEDIATE}>
					{t('difficulty.INTERMEDIATE')}
				</SelectItem>
				<SelectItem value={Difficulty.ADVANCED}>{t('difficulty.ADVANCED')}</SelectItem>
			</SelectContent>
		</Select>
	);
}
