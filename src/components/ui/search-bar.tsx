'use client';

import { Button, Input, useTheme } from '@/components/ui';
import { cn } from '@/lib';
import { AnimatePresence, motion } from 'framer-motion';
import { Search, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { VisuallyHidden } from './visually-hidden';
import { useTranslation } from '@/contexts';

interface SearchBarProps {
	className?: string;
	'aria-label'?: string;
}

type RouteItem = {
	label: string;
	path: string;
	keywords: string[];
};

// Add animation variants
const searchBarVariants = {
	hidden: { opacity: 0, y: -20 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.3,
			ease: 'easeOut',
		},
	},
};

const searchResultsVariants = {
	hidden: { opacity: 0, y: -10 },
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.2,
			ease: 'easeOut',
		},
	},
};

const searchItemVariants = {
	hidden: { opacity: 0, x: -20 },
	visible: (index: number) => ({
		opacity: 1,
		x: 0,
		transition: {
			duration: 0.2,
			delay: index * 0.05,
			ease: 'easeOut',
		},
	}),
};

function FloatSearchBar(props: SearchBarProps) {
	const { className } = props;
	const ariaLabel = (props as any)['aria-label'];
	const [isOpen, setIsOpen] = useState(false);
	const [searchQuery, setSearchQuery] = useState('');
	const [searchResults, setSearchResults] = useState<RouteItem[]>([]);
	const [selectedIndex, setSelectedIndex] = useState(-1);
	const [screenReaderStatus, setScreenReaderStatus] = useState('');
	const [mounted, setMounted] = useState(false); // Add mounted state
	const router = useRouter();
	const searchRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLInputElement>(null);
	const { t } = useTranslation();
	const { theme } = useTheme();

	useEffect(() => {
		setMounted(true);
	}, []);

	// Memoize routes to prevent recreation on every render
	const routes = useMemo(
		() => [
			{
				label: t('nav.home'),
				path: '/',
				keywords: ['home', 'main', 'welcome', 'dashboard'],
			},
			{
				label: t('nav.collections'),
				path: '/collections',
				keywords: ['collections', 'vocabulary', 'terms', 'language'],
			},
			{
				label: t('nav.about'),
				path: '/about',
				keywords: ['about', 'info', 'information', 'help'],
			},
			{
				label: t('nav.privacy'),
				path: '/privacy',
				keywords: ['privacy', 'policy', 'data', 'protection', 'gdpr'],
			},
			{
				label: t('nav.terms'),
				path: '/terms',
				keywords: ['terms', 'conditions', 'legal', 'agreement', 'service'],
			},
		],
		[t]
	);

	const handleSearch = useCallback(
		(query: string) => {
			if (query) {
				const filtered = routes.filter(
					(route) =>
						route.label.toLowerCase().includes(query.toLowerCase()) ||
						route.keywords.some((keyword) =>
							keyword.toLowerCase().includes(query.toLowerCase())
						)
				);
				setSearchResults(filtered);
				setSelectedIndex(-1);

				const resultCount = filtered.length;
				setScreenReaderStatus(
					resultCount > 0
						? t('accessibility.searchResultsCount', { count: resultCount })
						: t('accessibility.noSearchResults')
				);
			} else {
				setSearchResults(routes);
				setScreenReaderStatus('');
			}
		},
		[routes, t]
	);

	const handleNavigate = useCallback(
		(path: string) => {
			router.push(path);
			setIsOpen(false);
			setSearchQuery('');
		},
		[router]
	);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent) => {
			if (e.key === 'ArrowDown') {
				e.preventDefault();
				setSelectedIndex((prev) => (prev < searchResults.length - 1 ? prev + 1 : prev));
			} else if (e.key === 'ArrowUp') {
				e.preventDefault();
				setSelectedIndex((prev) => (prev > 0 ? prev - 1 : prev));
			} else if (e.key === 'Enter' && selectedIndex >= 0) {
				e.preventDefault();
				handleNavigate(searchResults[selectedIndex].path);
			}
		},
		[searchResults, selectedIndex, handleNavigate]
	);

	useEffect(() => {
		handleSearch(searchQuery);
	}, [searchQuery, handleSearch]);

	useEffect(() => {
		if (isOpen && inputRef.current) {
			inputRef.current.focus();
		}
	}, [isOpen]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
				setIsOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	// Update screen reader status when selected index changes
	useEffect(() => {
		if (selectedIndex >= 0 && selectedIndex < searchResults.length) {
			setScreenReaderStatus(
				t('accessibility.selectedSearchResult', {
					result: searchResults[selectedIndex].label,
					position: selectedIndex + 1,
					total: searchResults.length,
				})
			);
		}
	}, [selectedIndex, searchResults, t]);

	// Function to handle item click
	const handleItemClick = (path: string, index: number) => {
		setSelectedIndex(index);
		handleNavigate(path);
	};

	// Function to handle item keyboard interaction
	const handleItemKeyDown = (e: React.KeyboardEvent, path: string) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			handleNavigate(path);
		}
	};

	// Conditional render based on mounted state
	if (!mounted) {
		// Render a placeholder or nothing until mounted on client
		return null;
	}

	return (
		<div className={cn('fixed top-0 left-0 right-0 z-50', className)} ref={searchRef}>
			{/* Visually hidden text for screen readers */}
			{screenReaderStatus && (
				<VisuallyHidden aria-live="polite">{screenReaderStatus}</VisuallyHidden>
			)}

			<AnimatePresence>
				{!isOpen ? (
					<motion.div
						key="search-button"
						initial="hidden"
						animate="visible"
						exit="hidden"
						variants={searchBarVariants}
						className="absolute top-4 left-1/2 transform -translate-x-1/2"
					>
						<Button
							onClick={() => setIsOpen(true)}
							variant="outline"
							className={cn(
								'bg-background/80 backdrop-blur-sm border shadow-lg px-4 py-2 rounded-full flex items-center gap-2 w-64 hover:bg-background/90 hover:shadow-xl transition-all duration-300 group',
								theme === 'dark' &&
									'border-gray-700 bg-gray-900/80 hover:bg-gray-900/90'
							)}
							aria-label={ariaLabel || t('search.openSearch')}
							aria-expanded={isOpen}
							aria-haspopup="listbox"
						>
							<Search
								className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors duration-200"
								aria-hidden="true"
							/>
							<span className="text-muted-foreground group-hover:text-primary transition-colors duration-200">
								{t('search.placeholder')}
							</span>
							{/* Indicate keyboard shortcut for screen readers */}
							<VisuallyHidden>
								{t('accessibility.searchShortcut', { key: '/ or Ctrl+K' })}
							</VisuallyHidden>
						</Button>
					</motion.div>
				) : (
					<motion.div
						key="search-input"
						initial="hidden"
						animate="visible"
						exit="hidden"
						variants={searchBarVariants}
						className="absolute top-4 left-1/2 transform -translate-x-1/2 w-96"
					>
						<div className="relative">
							<Input
								ref={inputRef}
								type="text"
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
								onKeyDown={handleKeyDown}
								className={cn(
									'w-full bg-background/80 backdrop-blur-sm border shadow-lg pr-10',
									theme === 'dark' && 'border-gray-700 bg-gray-900/80'
								)}
								placeholder={t('search.placeholder')}
								aria-label={t('search.searchInput')}
								aria-controls="search-results"
								aria-expanded={isOpen}
								aria-autocomplete="list"
								aria-activedescendant={
									selectedIndex >= 0
										? `search-result-${selectedIndex}`
										: undefined
								}
							/>
							<Button
								variant="ghost"
								size="icon"
								className="absolute right-2 top-1/2 transform -translate-y-1/2"
								onClick={() => {
									setIsOpen(false);
									setSearchQuery('');
								}}
								aria-label={t('search.closeSearch')}
							>
								<X className="h-4 w-4" />
							</Button>
						</div>

						<AnimatePresence>
							{searchResults.length > 0 && (
								<motion.div
									variants={searchResultsVariants}
									initial="hidden"
									animate="visible"
									exit="hidden"
									className={cn(
										'absolute mt-1 w-full rounded-md border bg-background/100 backdrop-blur-sm shadow-lg z-10 max-h-80 overflow-y-auto',
										theme === 'dark' && 'border-gray-700 bg-gray-900/100'
									)}
									id="search-results"
									role="listbox"
									aria-label={t('search.searchResults')}
									tabIndex={-1}
								>
									<div className="py-1">
										{searchResults.map((route, index) => (
											<motion.div
												key={route.path}
												custom={index}
												variants={searchItemVariants}
												initial="hidden"
												animate="visible"
												className={cn(
													'block w-full text-left px-4 py-2 hover:bg-accent/50 hover:text-accent-foreground transition-colors duration-200 cursor-pointer',
													selectedIndex === index &&
														'bg-accent text-accent-foreground',
													theme === 'dark' && 'hover:bg-gray-800'
												)}
												role="option"
												id={`search-result-${index}`}
												aria-selected={selectedIndex === index}
												onClick={() => handleItemClick(route.path, index)}
												onKeyDown={(e) => handleItemKeyDown(e, route.path)}
												tabIndex={selectedIndex === index ? 0 : -1}
												data-focus-visible-added={selectedIndex === index}
											>
												{route.label}
											</motion.div>
										))}
									</div>
								</motion.div>
							)}
						</AnimatePresence>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}

// Export SearchBar as an alias for FloatSearchBar
export const SearchBar = FloatSearchBar;
