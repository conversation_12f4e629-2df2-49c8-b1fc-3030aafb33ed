import { Language, PartsOfSpeech, Prisma } from '@prisma/client';
import { z } from 'zod';

export const RandomWordSchema = z.object({
	term: z.string(),
	partOfSpeech: z.array(z.nativeEnum(PartsOfSpeech)),
	meaning: z.array(
		z.object({
			EN: z.string(),
			VI: z.string(),
		})
	),
});

export type RandomWord = z.infer<typeof RandomWordSchema>;

export const RandomWordDetailSchema = z.object({
	term: z.string(),
	language: z.enum([Language.EN]),
	definitions: z.array(
		z.object({
			pos: z.array(z.nativeEnum(PartsOfSpeech)),
			ipa: z.string(),
			explains: z.array(
				z.object({
					EN: z.string(),
					VI: z.string(),
				})
			),
			examples: z.array(
				z.object({
					EN: z.string(),
					VI: z.string(),
				})
			),
		})
	),
});

export type RandomWordDetail = z.infer<typeof RandomWordDetailSchema>;

export type WordDetail = Prisma.WordGetPayload<{
	include: {
		definitions: {
			include: {
				examples: true;
				explains: true;
			};
		};
	};
}>;

export type ReviewWordDetail = Prisma.LastSeenWordGetPayload<{}> & {
	retention_score: number;
	priority_score: number;
	word: WordDetail;
};
