'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { generateToken } from '@/backend/utils/token.util';
import { getAuthService } from '@/backend/wire';
import { config } from '@/config';
import { Provider } from '@prisma/client';
import { cookies } from 'next/headers';

/**
 * Logs out the current user by deleting the auth token cookie.
 * This function assumes it's called in a context where it can modify cookies.
 * @throws {Error} If deleting the cookie fails.
 */
export async function logoutApi(): Promise<void> {
	try {
		const cookieStore = await cookies();
		cookieStore.delete(config.auth.jwtCookieName);
	} catch (error) {
		console.error('Failed to logout user by deleting cookie:', error);
		// Throwing an error makes the API caller aware of the failure.
		throw new Error('Logout failed. Please try again.');
	}
}

/**
 * Handles provider login (e.g., Telegram) by:
 * 1. Authenticating the user via the AuthService using the provider and providerId.
 * 2. Generating a JWT token for the authenticated user.
 * 3. Setting the token in an HTTP-only cookie.
 *
 * @param provider - The authentication provider (e.g., `Provider.TELEGRAM`).
 * @param providerId - The user's unique identifier from the specified provider.
 * @throws {ValidationError} If `provider` or `providerId` is missing.
 * @throws {UnauthorizedError} If authentication fails (e.g., user not found with provider details, or service indicates failure).
 * @throws {Error} If token generation or cookie setting fails, or for other unexpected errors.
 */
export async function providerLoginApi(provider: Provider, providerId: string): Promise<void> {
	if (!provider) {
		throw new ValidationError('Provider is required for login.');
	}
	if (!providerId) {
		throw new ValidationError('Provider ID is required for login.');
	}
	if (!Object.values(Provider).includes(provider)) {
		throw new ValidationError(`Invalid provider specified: ${provider}`);
	}

	const authService = getAuthService();
	try {
		// Assumes authService.providerLogin returns a user object or throws/returns null on failure.
		const user = await authService.providerLogin(provider, providerId);
		if (!user) {
			// This case handles if the service returns null/undefined for an unsuccessful authentication
			// instead of throwing an error itself.
			throw new UnauthorizedError(
				`Authentication failed for provider "${provider}". User not found or credentials invalid.`
			);
		}

		// Assumes generateToken utility takes the user object and returns a JWT string.
		const token = await generateToken(user);
		if (!token) {
			// This case handles if token generation itself fails.
			console.error(`Token generation failed for user ${user.id} after provider login.`);
			throw new Error('Login failed due to an issue generating authentication token.');
		}

		const cookieStore = await cookies();

		cookieStore.set({
			name: config.auth.jwtCookieName,
			value: token,
			httpOnly: true,
			secure: config.server.env === 'production', // Ensure cookie is only sent over HTTPS in production
			sameSite: 'strict', // Helps prevent CSRF
			path: '/', // Cookie available for all paths
			maxAge: config.auth.jwtExpiresIn, // Expiration time for the cookie
		});
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error; // Rethrow known error types
		}
		// Log the detailed error for server-side debugging
		console.error(
			`Provider login process failed for provider "${provider}", provider ID "${providerId}":`,
			error
		);
		// Throw a generic error for the client to avoid leaking sensitive details
		throw new Error('Login failed. Please check your credentials or try again later.');
	}
}
