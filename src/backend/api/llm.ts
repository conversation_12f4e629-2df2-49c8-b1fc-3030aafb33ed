'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import {
	AnswerEvaluationResult,
	EvaluateAnswersParams,
	EvaluateTranslationParams,
	GenerateQuestionsParams,
	TranslationEvaluationResult,
	GrammarPracticeParams,
	GrammarPracticeResultItem,
} from '@/backend/services'; // Assuming these types are exported or accessible
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { RandomWord } from '@/models';
import { Difficulty, Language, Word } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

/**
 * Generates random terms (words) for the authenticated user based on keywords,
 * with options to exclude terms from certain collections.
 * Uses a local JSON file for caching in development.
 * @param keywordTerms - Array of keyword strings to base generation on.
 * @param maxTerms - Maximum number of terms to generate (1-50).
 * @param excludeCollectionIds - Optional array of collection IDs whose terms should be excluded.
 * @param source_language - The user's native language.
 * @param target_language - The language being learned.
 * @returns A promise that resolves to an array of RandomWord objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If keywordTerms are missing/empty or maxTerms is out of range, or languages are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateRandomWordsApi(
	keywordTerms: string[],
	maxTerms: number,
	excludeCollectionIds: string[] = [],
	source_language: Language, // Add source_language
	target_language: Language // Add target_language
): Promise<RandomWord[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate random words.'
		);
	}

	if (!Array.isArray(keywordTerms) || keywordTerms.length === 0) {
		throw new ValidationError('Keyword terms array is required and cannot be empty.');
	}
	if (keywordTerms.some((term) => typeof term !== 'string' || term.trim() === '')) {
		throw new ValidationError('Each keyword term must be a non-empty string.');
	}
	if (typeof maxTerms !== 'number' || maxTerms < 1 || maxTerms > 50) {
		throw new ValidationError('Max terms must be a number between 1 and 50.');
	}
	if (
		excludeCollectionIds &&
		(!Array.isArray(excludeCollectionIds) ||
			excludeCollectionIds.some((id) => typeof id !== 'string' || id.trim() === ''))
	) {
		throw new ValidationError(
			'Each excludeCollectionId must be a non-empty string if provided.'
		);
	}
	// Add language validation
	if (!Object.values(Language).includes(source_language)) {
		throw new ValidationError(`Invalid source language provided: ${source_language}`);
	}
	if (!Object.values(Language).includes(target_language)) {
		throw new ValidationError(`Invalid target language provided: ${target_language}`);
	}

	const filePath = path.join(process.cwd(), 'randomWordsResult.json');

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			// Add type assertion for safety, though JSON.parse returns any
			return JSON.parse(fileContent) as RandomWord[];
		} catch (err: any) {
			// If file doesn't exist (ENOENT), it's fine, we'll generate and save.
			// For other errors, log a warning but proceed to generate.
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
		}
	}

	const llmService = getLLMService();
	try {
		// The original GenerateRandomTermsQuery had an `excludesTerms` parameter.
		// This API signature doesn't. Assuming an empty array for it.
		// If `excludesTerms` needs to be passed from the client, update the API signature.
		const result = await llmService.generateRandomTerms({
			userId: userId,
			keywordTerms: keywordTerms,
			excludesTerms: [], // Defaulting to empty array as it's not in API params
			maxTerms: maxTerms,
			excludeCollectionIds: excludeCollectionIds,
			source_language: source_language, // Pass source_language
			target_language: target_language, // Pass target_language
		});

		if (process.env.NODE_ENV === 'development') {
			try {
				await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save random words cache file ${filePath}:`,
					err.message
				);
			}
		}
		return result;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateRandomWordsApi for user ${userId}:`, error);
		throw new Error('Failed to generate random words. Please try again.');
	}
}

/**
 * Generates detailed information for a list of terms.
 * Uses a local JSON file for caching in development, with filenames based on terms.
 * This API does not require authentication as per its original design.
 * If user-specific details or auth is needed, `auth()` call should be added.
 * @param terms - Array of term strings to get details for (max 10).
 * @param source_language - The user's native language.
 * @param target_language - The language being learned.
 * @returns A promise that resolves to an array of Word objects.
 * @throws {ValidationError} If terms array is missing, empty, exceeds max length, or languages are invalid.
 * @throws {Error} If generation fails for other reasons.
 */
export async function generateWordDetailsApi(
	terms: string[],
	source_language: Language, // Add source_language
	target_language: Language // Add target_language
): Promise<Word[]> {
	if (!Array.isArray(terms) || terms.length === 0) {
		throw new ValidationError('Terms array is required and cannot be empty.');
	}
	if (terms.length > 10) {
		throw new ValidationError('Maximum of 10 terms allowed for detail generation at a time.');
	}
	if (terms.some((term) => typeof term !== 'string' || term.trim() === '')) {
		throw new ValidationError('Each term must be a non-empty string.');
	}
	// Add language validation
	if (!Object.values(Language).includes(source_language)) {
		throw new ValidationError(`Invalid source language provided: ${source_language}`);
	}
	if (!Object.values(Language).includes(target_language)) {
		throw new ValidationError(`Invalid target language provided: ${target_language}`);
	}

	// Generate a filename based on the terms for caching
	const sortedTerms = [...terms].sort();
	const filenameBase = sortedTerms.map((term) => term.replace(/[^a-zA-Z0-9_]+/g, '_')).join('_');
	// Include languages in cache key to avoid collision if same terms are requested for different languages
	const filePath = path.join(
		process.cwd(),
		`wordDetails_${filenameBase}_${source_language}_${target_language}.json`
	);

	if (process.env.NODE_ENV === 'development') {
		try {
			const fileContent = await fs.readFile(filePath, 'utf-8');
			return JSON.parse(fileContent) as Word[];
		} catch (err: any) {
			if (err.code !== 'ENOENT') {
				console.warn(
					`Development mode: Could not read cache file ${filePath}. Will regenerate. Error: ${err.message}`
				);
			}
			// Fall through to generate if file not found or other read error
		}
	}

	const llmService = getLLMService();
	try {
		// Pass source_language and target_language to the service method
		const result = await llmService.generateWordDetails(
			terms,
			source_language,
			target_language
		);

		if (process.env.NODE_ENV === 'development') {
			try {
				await fs.writeFile(filePath, JSON.stringify(result, null, 2), 'utf-8');
			} catch (err: any) {
				console.error(
					`Development mode: Failed to save word details cache file ${filePath}:`,
					err.message
				);
			}
		}
		return result;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error('Error in generateWordDetailsApi:', error);
		throw new Error('Failed to generate word details. Please try again.');
	}
}

/**
 * Generates a specified number of paragraphs based on keywords, language, difficulty, and length.
 * This API requires authentication.
 * @param keywords - Array of keyword strings to base generation on.
 * @param language - The language of the paragraphs to be generated (source language for user).
 * @param difficulty - The difficulty level of the paragraphs.
 * @param length - The desired length of the paragraphs.
 * @param count - The number of paragraphs to generate (1-5).
 * @returns A promise that resolves to an array of generated paragraph strings.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If paragraph generation fails for other reasons.
 */
export async function generateParagraphApi(
	keywords: string[],
	language: Language,
	difficulty: Difficulty,
	count: number,
	sentenceCount?: number
): Promise<string[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate paragraphs.'
		);
	}

	if (!Array.isArray(keywords) || keywords.length === 0) {
		throw new ValidationError('Keywords array is required and cannot be empty.');
	}
	if (keywords.some((kw) => typeof kw !== 'string' || kw.trim() === '')) {
		throw new ValidationError('Each keyword must be a non-empty string.');
	}
	if (!Object.values(Language).includes(language)) {
		throw new ValidationError(`Invalid language provided: ${language}`);
	}
	if (!Object.values(Difficulty).includes(difficulty)) {
		throw new ValidationError(`Invalid difficulty provided: ${difficulty}`);
	}
	if (typeof count !== 'number' || count < 1 || count > 5) {
		throw new ValidationError('Count must be a number between 1 and 5.');
	}

	const llmService = getLLMService();
	try {
		const paragraphs = await llmService.generateParagraph({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
			// userId is not directly used by llmService.generateParagraph in the provided service code,
			// but it's good to have it if the service evolves to use it for personalization or logging.
		});
		return paragraphs;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateParagraphApi for user ${userId}:`, error);
		throw new Error('Failed to generate paragraphs. Please try again.');
	}
}

/**
 * Generates paragraphs with intentional grammar errors based on provided keywords and difficulty.
 * @param params - Parameters for generating paragraphs with errors.
 * @returns An array of generated paragraphs with errors.
 * @throws {ValidationError} If input parameters are invalid.
 * @throws {UnauthorizedError} If the user is not authenticated.
 */
export async function generateGrammarPracticeApi(
	params: GrammarPracticeParams
): Promise<GrammarPracticeResultItem[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError('User not authenticated for generating paragraphs.');
	}

	// Manual validation for GenerateParagraphsWithErrorsParams
	if (!Array.isArray(params.keywords) || params.keywords.length === 0) {
		throw new ValidationError('Keywords array is required and cannot be empty.');
	}
	if (params.keywords.some((kw) => typeof kw !== 'string' || kw.trim() === '')) {
		throw new ValidationError('Each keyword must be a non-empty string.');
	}
	if (!Object.values(Language).includes(params.language)) {
		throw new ValidationError(`Invalid language provided: ${params.language}`);
	}
	if (!Object.values(Difficulty).includes(params.difficulty)) {
		throw new ValidationError(`Invalid difficulty provided: ${params.difficulty}`);
	}
	if (typeof params.count !== 'number' || params.count < 1 || params.count > 5) {
		throw new ValidationError('Count must be a number between 1 and 5.');
	}
	if (
		params.sentenceCount !== undefined &&
		(typeof params.sentenceCount !== 'number' || params.sentenceCount < 1)
	) {
		throw new ValidationError('Sentence count must be a positive number if provided.');
	}
	if (
		params.errorDensity !== undefined &&
		!['low', 'medium', 'high'].includes(params.errorDensity)
	) {
		throw new ValidationError('Error density must be "low", "medium", or "high" if provided.');
	}

	const llmService = getLLMService();
	try {
		const paragraphs = await llmService.generateGrammarPractice(params);
		return paragraphs;
	} catch (error) {
		console.error('Error in generateParagraphsWithErrorsApi:', error);
		if (error instanceof Error) {
			throw error;
		}
		throw new Error('Failed to generate paragraphs with errors due to an internal issue.');
	}
}

/**
 * Evaluates a user's translation of a given text.
 * This API requires authentication.
 * @param params - Object containing original_text, translated_text, source_language, and target_language.
 * @returns A promise that resolves to a TranslationEvaluationResult object.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If translation evaluation fails for other reasons.
 */
export async function evaluateTranslationApi(
	params: EvaluateTranslationParams
): Promise<TranslationEvaluationResult> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to evaluate translations.'
		);
	}

	const { original_text, translated_text, source_language, target_language } = params;

	if (!original_text || typeof original_text !== 'string' || original_text.trim() === '') {
		throw new ValidationError('Original text is required and must be a non-empty string.');
	}
	if (!translated_text || typeof translated_text !== 'string' || translated_text.trim() === '') {
		throw new ValidationError('Translated text is required and must be a non-empty string.');
	}
	if (!Object.values(Language).includes(source_language)) {
		throw new ValidationError(`Invalid source language provided: ${source_language}`);
	}
	if (!Object.values(Language).includes(target_language)) {
		throw new ValidationError(`Invalid target language provided: ${target_language}`);
	}
	if (source_language === target_language) {
		throw new ValidationError('Source and target languages must be different.');
	}

	const llmService = getLLMService();
	try {
		const evaluation = await llmService.evaluateTranslation({
			original_text,
			translated_text,
			source_language,
			target_language,
		});
		return evaluation;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in evaluateTranslationApi for user ${userId}:`, error);
		throw new Error('Failed to evaluate translation. Please try again.');
	}
}

/**
 * Generates a specified number of questions based on a paragraph.
 * This API requires authentication.
 * @param params - Object containing paragraph, language, and questionCount.
 * @returns A promise that resolves to an array of generated question strings.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If question generation fails for other reasons.
 */
export async function generateQuestionsApi(params: GenerateQuestionsParams): Promise<string[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to generate questions.'
		);
	}

	const { paragraph, language, questionCount } = params;

	if (!paragraph || typeof paragraph !== 'string' || paragraph.trim() === '') {
		throw new ValidationError('Paragraph is required and must be a non-empty string.');
	}
	if (!Object.values(Language).includes(language)) {
		throw new ValidationError(`Invalid language provided: ${language}`);
	}
	if (typeof questionCount !== 'number' || questionCount < 1 || questionCount > 10) {
		// Max 10 questions for now, can be adjusted
		throw new ValidationError('Question count must be a number between 1 and 10.');
	}

	const llmService = getLLMService();
	try {
		const questions = await llmService.generateQuestions({
			paragraph,
			language,
			questionCount,
		});
		return questions;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in generateQuestionsApi for user ${userId}:`, error);
		throw new Error('Failed to generate questions. Please try again.');
	}
}

/**
 * Evaluates user's answers to questions based on a paragraph.
 * This API requires authentication.
 * @param params - Object containing paragraph, questions, answers, qna_language, and feedback_native_language.
 * @returns A promise that resolves to an array of AnswerEvaluationResult objects.
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {ValidationError} If any input parameters are invalid.
 * @throws {Error} If answer evaluation fails for other reasons.
 */
export async function evaluateAnswersApi(
	params: EvaluateAnswersParams
): Promise<AnswerEvaluationResult[]> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: User must be authenticated to evaluate answers.'
		);
	}

	const { paragraph, questions, answers, qna_language, feedback_native_language } = params;

	if (!paragraph || typeof paragraph !== 'string' || paragraph.trim() === '') {
		throw new ValidationError('Paragraph is required and must be a non-empty string.');
	}
	if (!Array.isArray(questions) || questions.length === 0) {
		throw new ValidationError('Questions array is required and cannot be empty.');
	}
	if (questions.some((q) => typeof q !== 'string' || q.trim() === '')) {
		throw new ValidationError('Each question must be a non-empty string.');
	}
	if (!Array.isArray(answers) || answers.length === 0) {
		throw new ValidationError('Answers array is required and cannot be empty.');
	}
	if (answers.some((a) => typeof a !== 'string' || a.trim() === '')) {
		// Allow empty string for an answer if user skipped, but still validate type
		throw new ValidationError('Each answer must be a string.');
	}
	if (questions.length !== answers.length) {
		throw new ValidationError('The number of questions and answers must match.');
	}
	if (!Object.values(Language).includes(qna_language)) {
		throw new ValidationError(`Invalid Q&A language provided: ${qna_language}`);
	}
	if (!Object.values(Language).includes(feedback_native_language)) {
		throw new ValidationError(
			`Invalid feedback native language provided: ${feedback_native_language}`
		);
	}
	if (qna_language === feedback_native_language) {
		// This might be a valid scenario depending on requirements, but good to validate if they must be different.
		// For now, allowing them to be the same. If they must be different, uncomment:
		// throw new ValidationError('Q&A language and feedback native language must be different.');
	}

	const llmService = getLLMService();
	try {
		const evaluations = await llmService.evaluateAnswers({
			paragraph,
			questions,
			answers,
			qna_language,
			feedback_native_language,
		});
		return evaluations;
	} catch (error) {
		if (error instanceof ValidationError || error instanceof UnauthorizedError) {
			throw error;
		}
		console.error(`Error in evaluateAnswersApi for user ${userId}:`, error);
		throw new Error('Failed to evaluate answers. Please try again.');
	}
}
