import {
	BaseRepository,
	BaseRepositoryImpl,
	CollectionRepository,
	CollectionRepositoryImpl,
	KeywordRepository,
	KeywordRepositoryImpl,
	LastSeenWordRepository,
	LastSeenWordRepositoryImpl,
	UserRepository,
	UserRepositoryImpl,
	WordRepository,
	WordRepositoryImpl,
} from '@/backend/repositories';
import {
	AuthService,
	AuthServiceImpl,
	CacheService,
	CollectionService,
	CollectionServiceImpl,
	FeedbackService,
	FeedbackServiceImpl,
	KeywordService,
	KeywordServiceImpl,
	LLMService,
	LastSeenWordService,
	LastSeenWordServiceImpl,
	UserService,
	UserServiceImpl,
	WordService,
	WordServiceImpl,
} from '@/backend/services';
import { Feedback, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// --- Lazily Initialized Repository Instances ---
let collectionRepository: CollectionRepository;
export const getCollectionRepository = (): CollectionRepository =>
	collectionRepository || (collectionRepository = new CollectionRepositoryImpl(prisma));

let keywordRepository: KeywordRepository;
export const getKeywordRepository = (): KeywordRepository =>
	keywordRepository || (keywordRepository = new KeywordRepositoryImpl(prisma));

let userRepository: UserRepository;
export const getUserRepository = (): UserRepository =>
	userRepository || (userRepository = new UserRepositoryImpl(prisma));

let wordRepository: WordRepository;
export const getWordRepository = (): WordRepository =>
	wordRepository || (wordRepository = new WordRepositoryImpl(prisma));

let lastSeenWordRepository: LastSeenWordRepository;
export const getLastSeenWordRepository = (): LastSeenWordRepository =>
	lastSeenWordRepository || (lastSeenWordRepository = new LastSeenWordRepositoryImpl(prisma));

let feedbackRepository: BaseRepository<Feedback>;
export const getFeedbackRepository = (): BaseRepository<Feedback> =>
	feedbackRepository || (feedbackRepository = new BaseRepositoryImpl<Feedback>(prisma.feedback));

// --- Lazily Initialized Service Instances with circular dependency handling ---
// Define service getters before instantiation to handle circular dependencies
let userService: UserService | null = null;
let authService: AuthService | null = null;
let cacheService: CacheService | null = null;
let feedbackService: FeedbackService | null = null;
let lastSeenWordService: LastSeenWordService | null = null;
let wordService: WordService | null = null;
let collectionService: CollectionService | null = null;
let llmService: LLMService | null = null;
let keywordService: any = null;

export const getUserService = (): UserService => {
	if (!userService) {
		userService = new UserServiceImpl(getUserRepository);
	}
	return userService;
};

export const getAuthService = (): AuthService => {
	if (!authService) {
		authService = new AuthServiceImpl(getUserService);
	}
	return authService;
};

export const getCacheService = (): CacheService => {
	if (!cacheService) {
		cacheService = new CacheService();
	}
	return cacheService;
};

export const getFeedbackService = (): FeedbackService => {
	if (!feedbackService) {
		feedbackService = new FeedbackServiceImpl(getFeedbackRepository);
	}
	return feedbackService;
};

export const getLastSeenWordService = (): LastSeenWordService => {
	if (!lastSeenWordService) {
		lastSeenWordService = new LastSeenWordServiceImpl(getLastSeenWordRepository);
	}
	return lastSeenWordService;
};

// Services with potential circular dependencies
export const getLLMService = (): LLMService => {
	if (!llmService) {
		llmService = new LLMService(getWordService);
	}
	return llmService;
};

export const getWordService = (): WordService => {
	if (!wordService) {
		wordService = new WordServiceImpl(
			getWordRepository,
			getCollectionService,
			getLastSeenWordService
		);
	}
	return wordService;
};

export const getCollectionService = (): CollectionService => {
	if (!collectionService) {
		collectionService = new CollectionServiceImpl(
			getCollectionRepository,
			getLLMService,
			getWordService
		);
	}
	return collectionService;
};

export const getKeywordService = (): KeywordService => {
	if (!keywordService) keywordService = new KeywordServiceImpl(getKeywordRepository);
	return keywordService;
};
