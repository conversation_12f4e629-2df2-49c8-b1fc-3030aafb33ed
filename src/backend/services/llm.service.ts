import { config } from '@/config';
import { Random<PERSON>ord, RandomWordDetailSchema, RandomWordSchema } from '@/models';
import { Difficulty, Language, Word } from '@prisma/client';
import OpenAI from 'openai';
import { zodResponseFormat } from 'openai/helpers/zod';
import { z } from 'zod';
import { WordService } from '.';

// Interface for existing method, updated
interface GenerateParagraphParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number; // Added count for multiple paragraphs
	sentenceCount?: number; // desired number of sentences
}

// Interfaces for existing methods (remains the same, but listed for completeness)
interface GenerateRandomTermsParams {
	userId: string;
	keywordTerms: string[];
	excludesTerms: string[];
	maxTerms: number;
	excludeCollectionIds: string[];
	source_language: Language;
	target_language: Language;
}

interface GenerateExercisesParams {
	paragraph: string;
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
}

export interface AnalyzeParagraphRequest {
	content: string;
	language: Language;
}

// Interfaces for Question & Answer features
export interface GenerateQuestionsParams {
	paragraph: string;
	language: Language;
	questionCount: number;
}

export interface EvaluateAnswersParams {
	paragraph: string;
	questions: string[];
	answers: string[];
	qna_language: Language; // Language of the questions and answers
	feedback_native_language: Language; // User's native language for feedback
}

export interface GrammarPracticeParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number;
	sentenceCount?: number;
	errorDensity?: 'low' | 'medium' | 'high';
}

export interface GrammarPracticeResultItem {
	paragraphWithErrors: string;
	correctedParagraph: string;
	allErrors: Array<{
		errorText: string;
		correctedText: string;
		errorType: string;
		explanation: string;
	}>;
}

export interface AnswerEvaluationResult {
	question: string;
	answer: string;
	feedback: {
		qna_feedback_text: string; // Feedback in the Q&A language
		native_feedback_text: string; // Feedback in the user's native language
	};
	score?: number;
	is_correct?: boolean;
	suggested_answer?: string;
}

// Interfaces for the new evaluateTranslation method
export interface EvaluateTranslationParams {
	original_text: string;
	translated_text: string;
	source_language: Language;
	target_language: Language;
}

export interface TranslationEvaluationResult {
	feedback: {
		source_language: string;
		target_language: string;
	};
	score?: number;
	suggestions?: {
		source_language: string[];
		target_language: string[];
	};
}

// Zod schemas for new/updated methods
const ExerciseSchema = z.object({
	type: z.string(),
	question: z.string(),
	answer: z.string(),
	options: z.array(z.string()).optional(),
	explanation: z.string().optional(),
});

const ExercisesResponseSchema = z.object({
	exercises: z.array(ExerciseSchema),
});

// Zod schema for generating questions
const GeneratedQuestionsSchema = z.object({
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for evaluating a single answer
const AnswerEvaluationSchema = z.object({
	question: z.string().describe('The original question this evaluation pertains to.'),
	answer: z.string().describe("The user's answer that was evaluated."),
	feedback: z
		.object({
			qna_feedback_text: z
				.string()
				.describe(
					"Detailed feedback in the Q&A language ({qna_language}) on the user's answer, explaining its strengths and weaknesses."
				),
			native_feedback_text: z
				.string()
				.describe(
					"The same detailed feedback, but translated or adapted into the user's native language ({feedback_native_language}) for better understanding."
				),
		})
		.describe(
			"Detailed feedback on the user's answer, provided in both the Q&A language and the user's native language."
		),
	score: z
		.number()
		.min(1)
		.max(5)
		.optional()
		.describe('A score from 1 (poor) to 5 (excellent) for the answer.'),
	is_correct: z
		.boolean()
		.optional()
		.describe(
			'A simple true/false indicating if the answer is fundamentally correct regarding the paragraph content.'
		),
	suggested_answer: z
		.string()
		.optional()
		.describe(
			'An example of an ideal or improved answer to the question, based on the paragraph.'
		),
});

// Zod schema for grammar correction
const GrammarCorrectionResponseSchema = z.object({
	correctedParagraph: z
		.string()
		.describe('The paragraph with corrections applied based on selected words.'),
	corrections: z
		.array(
			z.object({
				originalWord: z.string().describe('The word that was identified as incorrect.'),
				suggestedWord: z.string().describe('The suggested correction for the word.'),
				explanation: z
					.string()
					.describe(
						'An explanation of why the word was incorrect and how it was corrected.'
					),
			})
		)
		.describe('An array of specific corrections made.'),
	allErrors: z
		.array(
			z.object({
				errorText: z.string().describe('The incorrect text found in the paragraph.'),
				correctedText: z.string().describe('The corrected version of the error.'),
				errorType: z
					.string()
					.describe('Type of error (grammar, spelling, word choice, etc.).'),
				explanation: z.string().describe('Explanation of the error and correction.'),
			})
		)
		.describe('All grammar errors found in the paragraph, regardless of user selection.'),
	score: z
		.number()
		.min(1)
		.max(10)
		.optional()
		.describe(
			"A score from 1 (poor) to 10 (excellent) for the user's grammar identification skills."
		),
});

// Zod schema for evaluating all answers
const AllAnswersEvaluationSchema = z.object({
	evaluations: z
		.array(AnswerEvaluationSchema)
		.describe('An array of evaluations, one for each question-answer pair.'),
});

const TranslationEvaluationSchema = z.object({
	feedback: z
		.object({
			source_language: z
				.string()
				.describe('Overall feedback in the original text language (source_language).'),
			target_language: z
				.string()
				.describe("Overall feedback in the user's translation language (target_language)."),
		})
		.describe(
			"Overall feedback on the translation's quality, correctness, and areas for improvement, provided in both source and target languages."
		),
	score: z
		.number()
		.min(1)
		.max(10)
		.optional()
		.describe('An overall score from 1 (poor) to 10 (excellent) for the translation.'),
	suggestions: z
		.object({
			source_language: z
				.array(z.string())
				.optional()
				.describe(
					'Specific suggestions for improving the translation in the source_language, if any.'
				),
			target_language: z
				.array(z.string())
				.optional()
				.describe(
					'Specific suggestions for improving the translation in the target_language, if any.'
				),
		})
		.optional()
		.describe(
			'Specific suggestions for improving the translation, provided in both source and target languages, if any.'
		),
});

// New combined schema for grammar practice
const GrammarPracticeResponseSchema = z.array(
	z.object({
		paragraphWithErrors: z
			.string()
			.describe('Array of paragraphs containing intentional errors for practice.'),
		correctedParagraph: z
			.string()
			.describe('Array of the same paragraphs with all errors corrected.'),
		allErrors: z
			.array(
				z.object({
					errorText: z.string().describe('The incorrect text found in the paragraph.'),
					correctedText: z.string().describe('The corrected version of the error.'),
					errorType: z
						.string()
						.describe('Type of error (grammar, spelling, word choice, etc.).'),
					explanation: z.string().describe('Explanation of the error and correction.'),
				})
			)
			.describe(
				'All errors found in all paragraphs with their corrections and explanations.'
			),
	})
);

export class LLMService {
	private readonly openai: OpenAI;

	constructor(private readonly getWordService: () => WordService) {
		this.openai = new OpenAI({ apiKey: config.llm.openAIKey });
	}

	async generateRandomTerms(params: GenerateRandomTermsParams): Promise<RandomWord[]> {
		const { keywordTerms, excludesTerms, maxTerms, excludeCollectionIds } = params;

		const allFetchedWords: Word[] = (
			await Promise.all(
				excludeCollectionIds.map(async (id) =>
					this.getWordService().getWordsByCollection(params.userId, id)
				)
			)
		).flat();

		const seenIds = new Set<string | number>();
		const excludeCollectionsWords: Word[] = allFetchedWords.filter((word) => {
			const wordId = word.id;
			if (seenIds.has(wordId)) {
				return false;
			}
			seenIds.add(wordId);
			return true;
		});

		const allExcludes = [
			...excludesTerms,
			...excludeCollectionsWords.map((word: Word) => word.term),
		];

		const completion = await this.openai.beta.chat.completions.parse({
			model: config.llm.openAIModel,
			messages: [
				{
					role: 'system',
					content: `You are an expert vocabulary generator specializing in language learning content. Your mission is to create high-quality, contextually relevant vocabulary terms.

## TASK REQUIREMENTS:
1. **Quantity**: Generate exactly ${maxTerms} unique vocabulary terms
2. **Language**: All terms must be in ${params.target_language}
3. **Target Audience**: Suitable for learners whose native language is ${params.source_language}
4. **Thematic Relevance**: Each term must be semantically related to these keywords: ${keywordTerms.join(
						', '
					)}
5. **Exclusion Compliance**: Absolutely avoid these terms: ${allExcludes.join(', ')}

## QUALITY STANDARDS:
- Select terms that are practical and commonly used in real-world contexts
- Ensure appropriate difficulty level for language learners
- Prioritize terms that enhance vocabulary breadth in the given topic areas
- Include a mix of nouns, verbs, adjectives, and other word types when appropriate
- Focus on terms that are culturally relevant and useful for communication

## VALIDATION:
- Double-check that no generated term appears in the exclusion list
- Ensure each term is spelled correctly and properly formatted
- Verify thematic consistency across all generated terms

Provide exactly ${maxTerms} terms that meet all these criteria.`,
				},
			],
			temperature: 0.6,
			max_tokens: 6000,
			response_format: zodResponseFormat(
				z.object({
					words: z.array(RandomWordSchema),
				}),
				'event'
			),
		});

		const randomWords = completion.choices[0]?.message.parsed;
		if (!randomWords) {
			throw new Error('Failed to generate terms');
		}

		return randomWords.words as RandomWord[];
	}

	async generateWordDetails(
		terms: string[],
		source_language: Language,
		target_language: Language
	): Promise<Word[]> {
		const existingWords = await this.getWordService().getWordsByTerms(terms);
		const results: Word[] = [];

		const termsToProcess = new Set(terms);

		for (const word of existingWords) {
			results.push(word);
			termsToProcess.delete(word.term);
		}

		if (termsToProcess.size > 0) {
			const completion = await this.openai.beta.chat.completions.parse({
				model: config.llm.openAIModel,
				messages: [
					{
						role: 'system',
						content: `You are a comprehensive linguistic analyst and language learning content creator. Your expertise spans etymology, phonetics, semantics, and cross-cultural communication.

## TASK: Generate detailed word information for these terms in ${target_language}:
${Array.from(termsToProcess).join(', ')}

## REQUIRED COMPONENTS FOR EACH TERM:

### 1. ACCURATE DEFINITIONS
- Provide clear, concise definitions in ${target_language}
- Include primary and secondary meanings when applicable
- Ensure definitions are appropriate for language learners

### 2. PHONETIC TRANSCRIPTION (IPA)
- Provide accurate International Phonetic Alphabet transcription
- Use standard IPA symbols and conventions
- Include stress markers where applicable

### 3. BILINGUAL EXPLANATIONS
- **${target_language} explanation**: Detailed explanation in the target language
- **${source_language} explanation**: Clear explanation in the learner's native language
- Ensure explanations complement each other and aid comprehension

### 4. CONTEXTUAL EXAMPLES
- **${target_language} examples**: 2-3 natural, practical sentences showing proper usage
- **${source_language} examples**: Corresponding examples in the native language
- Examples should demonstrate different contexts and usage patterns
- Ensure examples are culturally appropriate and relevant

## QUALITY STANDARDS:
- Maintain linguistic accuracy and consistency
- Use natural, contemporary language in examples
- Ensure cultural sensitivity and appropriateness
- Provide information that enhances learning and retention
- Double-check all phonetic transcriptions for accuracy

Generate comprehensive, educational content for each specified term.`,
					},
				],
				temperature: 0.5,
				max_tokens: 12000,
				response_format: zodResponseFormat(
					z.object({
						words: z.array(RandomWordDetailSchema),
					}),
					'wordDetail'
				),
			});

			const details = completion.choices[0]?.message.parsed;
			if (!details) {
				throw new Error('Failed to generate word details');
			}

			for (const wordDetail of details.words) {
				results.push(
					await this.getWordService().createWordWithRandomWordDetail(wordDetail)
				);
			}
		}

		return results;
	}

	async generateParagraph(params: GenerateParagraphParams): Promise<string[]> {
		const { keywords, language, difficulty, count, sentenceCount } = params;
		const systemPrompt = `You are an expert language learning content creator specializing in translation practice materials. Your expertise lies in creating engaging, level-appropriate content specifically designed for translation exercises.
Create exactly ${count} distinct, high-quality paragraphs in ${language} that serve as excellent source material for translation practice.
- Target Level: ${difficulty.toLowerCase()} proficiency
- Translation Readiness: Create content that is meaningful and engaging to translate
- Vocabulary: Use words appropriate for ${difficulty.toLowerCase()} learners
- Grammar: Employ sentence structures that demonstrate proper usage and are suitable for translation practice
- Keywords: Focus on themes related to these keywords: ${keywords.join(', ')}
- Relevance: Maintain thematic coherence that supports vocabulary learning through translation
${
	sentenceCount != null
		? `Each paragraph should have approximately ${sentenceCount} sentences.`
		: ''
}
- Engagement: Create interesting, relatable content that motivates translation practice
- Cultural Context: Include culturally appropriate references that enhance translation learning
- Learning Reinforcement: Provide rich context that aids vocabulary retention through translation
- Practical Application: Use scenarios and topics that learners would realistically need to translate
- Translation Challenges: Include appropriate linguistic features that provide good translation practice
- Grammar: Ensure perfect grammatical accuracy to serve as reliable source material
- Spelling: Verify correct spelling throughout
- Distinctiveness: Each paragraph must be unique and offer different translation challenges
- Translation Suitability: Ensure content is meaningful and valuable when translated`;

		const responseSchema = z.object({
			paragraphs: z.array(z.string()),
		});

		const maxRetries = 3;
		let retryCount = 0;
		let generatedParagraphs: string[] | null = null;

		while (retryCount < maxRetries && !generatedParagraphs) {
			try {
				const completion = await this.openai.beta.chat.completions.parse({
					model: config.llm.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 1000 * count, // Consider adjusting if sentenceCount implies longer paragraphs
					response_format: zodResponseFormat(responseSchema, 'generateParagraphs'),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.paragraphs ||
					parsedResponse.paragraphs.length !== count
				) {
					throw new Error(
						`LLM did not return the expected number of paragraphs. Expected ${count}, got ${
							parsedResponse?.paragraphs?.length || 0
						}.`
					);
				}
				generatedParagraphs = parsedResponse.paragraphs;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);

				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraphs after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}

				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedParagraphs) {
			throw new Error('Failed to generate paragraphs after retries.');
		}

		return generatedParagraphs;
	}

	async generateExercises(
		params: GenerateExercisesParams
	): Promise<z.infer<typeof ExerciseSchema>[]> {
		const { paragraph, keywords, language, difficulty } = params;

		const systemPrompt = `You are a master exercise designer specializing in language learning pedagogy. Your expertise lies in creating diverse, engaging exercises that reinforce learning through multiple cognitive pathways.

## SOURCE MATERIAL:
**Paragraph in ${language}:**
"${paragraph}"

## EXERCISE CREATION MISSION:
Design 3-5 comprehensive exercises that maximize learning outcomes for ${difficulty.toLowerCase()} level students.

## EXERCISE REQUIREMENTS:

### 1. EXERCISE DIVERSITY
**Mandatory Types** (include at least one of each):
- **Fill in the Blank**: Test vocabulary and grammar in context
- **Multiple Choice**: Assess comprehension and vocabulary recognition
- **Matching**: Connect related concepts, words, or meanings

**Optional Additional Types**:
- True/False with justification
- Short answer comprehension
- Word formation/transformation
- Sentence reordering

### 2. CONTENT FOCUS
- **Primary Keywords**: Emphasize these terms: ${keywords.join(', ')}
- **Contextual Understanding**: Test comprehension of the paragraph's main ideas
- **Vocabulary Application**: Assess both recognition and production
- **Grammar Integration**: Include relevant grammatical structures

### 3. DIFFICULTY CALIBRATION
- **Level Appropriate**: Suitable for ${difficulty.toLowerCase()} proficiency
- **Progressive Challenge**: Vary difficulty within the exercise set
- **Scaffolded Learning**: Build from simpler to more complex tasks
- **Achievable Goals**: Ensure exercises are challenging but fair

### 4. EXERCISE COMPONENTS
For each exercise, provide:
- **Type**: Clear exercise category
- **Question**: Well-formulated, unambiguous prompt
- **Answer**: Correct, complete answer
- **Options**: For multiple choice, include 3-4 plausible distractors
- **Explanation**: Educational rationale for the correct answer

### 5. PEDAGOGICAL PRINCIPLES
- **Clear Instructions**: Unambiguous task descriptions
- **Meaningful Context**: Exercises should relate to real-world usage
- **Learning Reinforcement**: Each exercise should strengthen understanding
- **Error Prevention**: Avoid ambiguous or trick questions
- **Positive Learning**: Foster confidence and progress

## QUALITY STANDARDS:
- Ensure all exercises are directly based on the provided paragraph
- Verify that questions have single, clear correct answers
- Include educational explanations that enhance learning
- Maintain consistency in difficulty and style
- Test different aspects of language competency

Create exercises that not only assess but also teach and reinforce the target language skills.`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedExercises: z.infer<typeof ExerciseSchema>[] | null = null;

		while (retryCount < maxRetries && !generatedExercises) {
			try {
				const completion = await this.openai.beta.chat.completions.parse({
					model: config.llm.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 2000,
					response_format: zodResponseFormat(
						ExercisesResponseSchema,
						'generateExercises'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;
				if (!parsedResponse || !parsedResponse.exercises) {
					throw new Error('Empty or invalid response from LLM for exercises.');
				}
				generatedExercises = parsedResponse.exercises;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate exercises after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedExercises) {
			throw new Error('Failed to generate exercises after retries.');
		}

		return generatedExercises;
	}

	async evaluateTranslation(
		params: EvaluateTranslationParams
	): Promise<TranslationEvaluationResult> {
		const { original_text, translated_text, source_language, target_language } = params;

		const systemPrompt = `You are a world-class translation evaluation specialist with expertise in comparative linguistics, cultural adaptation, and language pedagogy. Your role is to provide comprehensive, constructive assessment of translation quality.

## TRANSLATION ASSESSMENT TASK:

**Original Text (${source_language}):**
"${original_text}"

**User's Translation (${target_language}):**
"${translated_text}"

## COMPREHENSIVE EVALUATION FRAMEWORK:

### 1. ACCURACY ANALYSIS
- **Semantic Fidelity**: How well does the translation convey the original meaning?
- **Completeness**: Are all elements of the original text represented?
- **Precision**: Are specific terms and concepts accurately rendered?
- **Context Preservation**: Is the situational context maintained?

### 2. LINGUISTIC QUALITY ASSESSMENT
- **Grammar**: Evaluate grammatical correctness in ${target_language}
- **Syntax**: Assess sentence structure and word order
- **Vocabulary**: Analyze word choice appropriateness and register
- **Idiomaticity**: Determine how natural the translation sounds

### 3. FLUENCY AND READABILITY
- **Natural Flow**: Does the translation read smoothly?
- **Coherence**: Are ideas logically connected?
- **Readability**: Is the text accessible to target language speakers?
- **Style Consistency**: Is the writing style appropriate and consistent?

### 4. CULTURAL AND CONTEXTUAL ADAPTATION
- **Cultural Sensitivity**: Are cultural references appropriately handled?
- **Register Matching**: Does the formality level match the original?
- **Audience Appropriateness**: Is the translation suitable for its intended readers?
- **Pragmatic Equivalence**: Are implied meanings preserved?

### 5. SCORING METHODOLOGY (1-10 scale):
- **9-10**: Exceptional - Professional quality with minimal issues
- **7-8**: Good - Accurate with minor linguistic improvements needed
- **5-6**: Adequate - Generally correct but needs refinement
- **3-4**: Needs Improvement - Significant accuracy or fluency issues
- **1-2**: Poor - Major problems affecting comprehension

## BILINGUAL FEEDBACK REQUIREMENTS:

### Detailed Feedback (Required):
- **source_language**: Comprehensive evaluation in ${source_language}
- **target_language**: Same evaluation content in ${target_language}

### Specific Suggestions (If applicable):
- **source_language**: Actionable improvement recommendations in ${source_language}
- **target_language**: Same recommendations in ${target_language}

## FEEDBACK PRINCIPLES:
- **Constructive**: Focus on improvement opportunities
- **Specific**: Provide concrete examples and alternatives
- **Educational**: Explain the reasoning behind suggestions
- **Encouraging**: Acknowledge strengths while addressing weaknesses
- **Actionable**: Offer practical steps for improvement

Provide a thorough, fair, and educational assessment that helps the user understand both their achievements and areas for growth in translation skills.`;

		try {
			const completion = await this.openai.beta.chat.completions.parse({
				model: config.llm.openAIModel,
				messages: [
					{
						role: 'system',
						content: systemPrompt,
					},
				],
				temperature: 0.3,
				max_tokens: 1000,
				response_format: zodResponseFormat(
					TranslationEvaluationSchema,
					'evaluateTranslation'
				),
			});

			const evaluationResult = completion.choices[0]?.message.parsed;

			if (!evaluationResult) {
				throw new Error('LLM did not return a valid evaluation.');
			}
			return evaluationResult as TranslationEvaluationResult;
		} catch (error) {
			console.error('Error evaluating translation with OpenAI:', error);
			throw new Error(
				`Failed to evaluate translation: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
		}
	}

	async generateQuestions(params: GenerateQuestionsParams): Promise<string[]> {
		const { paragraph, language, questionCount } = params;

		const systemPrompt = `You are an expert educational assessment designer specializing in reading comprehension and language learning evaluation. Your expertise lies in creating questions that effectively measure understanding while promoting deeper learning.

## SOURCE MATERIAL:
**Paragraph in ${language}:**
"${paragraph}"

## QUESTION GENERATION MISSION:
Create exactly ${questionCount} high-quality, pedagogically sound questions that comprehensively assess student understanding.

## QUESTION DESIGN FRAMEWORK:

### 1. COGNITIVE DIVERSITY
Distribute questions across different thinking levels:
- **Literal Comprehension** (30-40%): Direct information recall
- **Inferential Understanding** (40-50%): Reading between the lines
- **Critical Analysis** (10-20%): Evaluation and synthesis

### 2. CONTENT COVERAGE
- **Main Ideas**: Test understanding of central themes
- **Supporting Details**: Assess attention to specific information
- **Vocabulary in Context**: Evaluate word meaning comprehension
- **Cause and Effect**: Examine logical relationships
- **Sequence and Structure**: Test organizational understanding

### 3. QUESTION TYPES VARIETY
- **Factual Questions**: Who, what, when, where
- **Analytical Questions**: Why, how, what if
- **Vocabulary Questions**: Word meaning and usage
- **Inference Questions**: Implied meanings and conclusions
- **Application Questions**: Connecting to broader contexts

### 4. LANGUAGE LEARNING CONSIDERATIONS
- **Level Appropriate**: Suitable for ${language} learners
- **Clear Wording**: Unambiguous and accessible language
- **Cultural Sensitivity**: Appropriate for diverse backgrounds
- **Answerable**: Based solely on paragraph content
- **Educational Value**: Promotes language skill development

### 5. QUALITY STANDARDS
- **Clarity**: Each question should have one clear interpretation
- **Relevance**: Directly related to paragraph content
- **Difficulty Balance**: Mix of easier and more challenging questions
- **Completeness**: Cover different aspects of the text
- **Engagement**: Interesting and thought-provoking

## QUESTION FORMULATION GUIDELINES:
- Use varied question starters and structures
- Avoid yes/no questions unless they require explanation
- Ensure questions test understanding, not just memory
- Include questions that require synthesis of information
- Make questions specific enough to have definitive answers

## OUTPUT REQUIREMENTS:
Generate exactly ${questionCount} distinct, well-crafted questions that collectively provide a comprehensive assessment of paragraph comprehension. Each question should contribute to a complete picture of student understanding.

Format as JSON with "questions" key containing an array of ${questionCount} question strings.`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedQuestions: string[] | null = null;

		while (retryCount < maxRetries && !generatedQuestions) {
			try {
				const completion = await this.openai.beta.chat.completions.parse({
					model: config.llm.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.6,
					max_tokens: 500 + questionCount * 100, // Adjusted token limit
					response_format: zodResponseFormat(
						GeneratedQuestionsSchema,
						'generateQuestions'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.questions ||
					parsedResponse.questions.length !== questionCount
				) {
					throw new Error(
						`LLM did not return the expected number of questions. Expected ${questionCount}, got ${
							parsedResponse?.questions?.length || 0
						}.`
					);
				}
				generatedQuestions = parsedResponse.questions;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateQuestions, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate questions after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedQuestions) {
			throw new Error('Failed to generate questions after retries.');
		}
		return generatedQuestions;
	}

	async evaluateAnswers(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> {
		const { paragraph, questions, answers, qna_language, feedback_native_language } = params;

		if (questions.length !== answers.length) {
			throw new Error('The number of questions and answers must match.');
		}
		if (questions.length === 0) {
			return [];
		}

		let questionAnswerPairs = '';
		questions.forEach((q, i) => {
			questionAnswerPairs += `Question ${i + 1}: "${q}"\nUser's Answer ${i + 1}: "${
				answers[i]
			}"\n\n`;
		});

		const systemPrompt = `You are a master language learning assessment specialist with expertise in formative evaluation, constructive feedback, and multilingual education. Your mission is to provide comprehensive, encouraging, and educational evaluations that promote learning growth.

## ASSESSMENT CONTEXT:

**Reference Paragraph (${qna_language}):**
"${paragraph}"

**Question-Answer Pairs for Evaluation (${qna_language}):**
${questionAnswerPairs}

## COMPREHENSIVE EVALUATION FRAMEWORK:

### 1. MULTI-DIMENSIONAL ASSESSMENT
For each answer, evaluate:
- **Content Accuracy**: Factual correctness relative to the paragraph
- **Completeness**: How thoroughly the answer addresses the question
- **Relevance**: Direct connection to both question and source material
- **Language Quality**: Grammar, vocabulary, and expression in ${qna_language}
- **Understanding Depth**: Evidence of comprehension vs. surface-level response

### 2. SCORING METHODOLOGY (1-5 scale):
- **5 (Excellent)**: Complete, accurate, well-expressed answer
- **4 (Good)**: Mostly correct with minor gaps or language issues
- **3 (Satisfactory)**: Adequate understanding with some inaccuracies
- **2 (Needs Improvement)**: Partial understanding with significant issues
- **1 (Poor)**: Minimal understanding or major inaccuracies

### 3. CORRECTNESS DETERMINATION:
- **True**: Answer demonstrates fundamental understanding and accuracy
- **False**: Answer contains significant errors or misunderstandings
- Consider partial credit for answers with mixed accuracy

### 4. BILINGUAL FEEDBACK STRUCTURE:

#### qna_feedback_text (in ${qna_language}):
- Acknowledge strengths in the answer
- Identify specific areas for improvement
- Explain why certain aspects are correct or incorrect
- Provide constructive guidance for enhancement
- Use encouraging, supportive tone

#### native_feedback_text (in ${feedback_native_language}):
- Convey the same content as above
- Adapt cultural and linguistic nuances appropriately
- Ensure clarity for native language comprehension
- Maintain consistent tone and message

### 5. SUGGESTED ANSWER CRITERIA:
Provide when beneficial for learning:
- Model answers that demonstrate ideal responses
- Alternative phrasings that show flexibility
- Examples that clarify complex concepts
- Responses that highlight key information from the paragraph

## EVALUATION PRINCIPLES:

### Educational Focus:
- **Growth-Oriented**: Emphasize learning opportunities
- **Specific**: Provide concrete examples and explanations
- **Balanced**: Acknowledge both strengths and areas for improvement
- **Actionable**: Offer clear steps for improvement
- **Encouraging**: Maintain positive, supportive tone

### Cultural Sensitivity:
- Respect diverse learning backgrounds
- Acknowledge different communication styles
- Provide culturally appropriate feedback
- Consider language learning challenges

### Consistency Standards:
- Apply evaluation criteria uniformly
- Maintain consistent scoring standards
- Ensure feedback quality across all responses
- Preserve order matching the input sequence

## OUTPUT REQUIREMENTS:
Provide comprehensive evaluations for each question-answer pair that:
- Help students understand their performance
- Guide future learning efforts
- Build confidence while addressing weaknesses
- Demonstrate clear connection to source material
- Offer practical improvement strategies

Evaluate each response thoroughly and constructively, focusing on educational value and student growth.`;

		const maxRetries = 3;
		let retryCount = 0;
		let evaluationResults: AnswerEvaluationResult[] | null = null;

		while (retryCount < maxRetries && !evaluationResults) {
			try {
				const completion = await this.openai.beta.chat.completions.parse({
					model: config.llm.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.3,
					max_tokens: 1500 + questions.length * 300, // Adjusted token limit
					response_format: zodResponseFormat(
						AllAnswersEvaluationSchema,
						'evaluateAnswers'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.evaluations ||
					parsedResponse.evaluations.length !== questions.length
				) {
					throw new Error(
						`LLM did not return the expected number of evaluations. Expected ${
							questions.length
						}, got ${parsedResponse?.evaluations?.length || 0}.`
					);
				}
				evaluationResults = parsedResponse.evaluations as AnswerEvaluationResult[];
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (evaluateAnswers, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to evaluate answers after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}
		if (!evaluationResults) {
			throw new Error('Failed to evaluate answers after retries.');
		}
		return evaluationResults;
	}

	async generateGrammarPractice(
		params: GrammarPracticeParams
	): Promise<GrammarPracticeResultItem[]> {
		const {
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
			errorDensity = 'medium',
		} = params;

		const sentenceRequirement = sentenceCount
			? `Each paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		const systemPrompt = `You are a specialized language learning content creator with expertise in error analysis and pedagogical material design. Your mission is to create comprehensive grammar practice materials.

## CONTENT CREATION MISSION:
Generate exactly ${count} distinct paragraphs in ${language} for grammar practice, providing both error-filled versions and their corrections.

## DETAILED SPECIFICATIONS:

### 1. CONTENT FOUNDATION
- **Language**: ${language}
- **Proficiency Level**: ${difficulty.toLowerCase()}
- **Thematic Integration**: Naturally relative to ALL keywords: ${keywords.join(', ')}
- **Paragraph Count**: Exactly ${count} unique paragraphs
${sentenceRequirement ? `- **Length**: ${sentenceRequirement}` : ''}

### 2. ERROR INTEGRATION STRATEGY
**Error Density**: ${errorDensity} level
- **Low**: 1-2 subtle errors per paragraph
- **Medium**: 3-4 varied errors per paragraph
- **High**: 5-6 diverse errors per paragraph

### 3. ERROR TYPE DISTRIBUTION
Include realistic mistakes that ${difficulty.toLowerCase()} learners commonly make:

#### Grammar Errors (40-50%):
- Verb tense inconsistencies
- Subject-verb agreement mistakes
- Article usage errors (a/an/the)
- Preposition confusion
- Word order problems
- Conditional structure errors

#### Vocabulary Errors (25-35%):
- Word choice mistakes (similar words)
- False friends and cognates
- Collocation errors
- Register inappropriateness
- Literal translations from native language

#### Spelling and Mechanics (15-25%):
- Common spelling mistakes
- Punctuation errors
- Capitalization issues
- Apostrophe misuse

### 4. OUTPUT REQUIREMENTS:
For each paragraph, provide:
1. **Error Version**: The paragraph with intentional errors
2. **Corrected Version**: The same paragraph with all errors fixed
3. **Error Documentation**: Detailed list of all errors with explanations

### 5. ERROR DOCUMENTATION:
For each error, specify:
- **Paragraph Index**: Which paragraph (0-based)
- **Error Text**: The incorrect text
- **Corrected Text**: The correct version
- **Error Type**: Category of error (grammar, spelling, etc.)
- **Explanation**: Why it was wrong and how to fix it

## QUALITY STANDARDS:
- **Engaging Topics**: Interesting, relevant subject matter
- **Cultural Appropriateness**: Suitable for diverse learners
- **Coherent Narrative**: Despite errors, maintain logical flow
- **Keyword Integration**: Natural, meaningful use of required terms
- **Educational Value**: Content should be informative and useful
- **Realistic Mistakes**: Errors should reflect actual learner challenges
- **Level Appropriate**: Errors suitable for ${difficulty.toLowerCase()} proficiency

Generate exactly ${count} paragraphs with their corrections and comprehensive error documentation.`;

		const maxRetries = 3;
		let retryCount = 0;
		let result: GrammarPracticeResultItem[] = [];

		while (retryCount < maxRetries && !result) {
			try {
				const completion = await this.openai.beta.chat.completions.parse({
					model: config.llm.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: 2000 * count,
					response_format: zodResponseFormat(
						GrammarPracticeResponseSchema,
						'generateGrammarPractice'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;
				if (!parsedResponse) {
					throw new Error(`LLM did not return the expected number of paragraphs`);
				}
				result = parsedResponse;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateGrammarPractice, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate grammar practice after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate grammar practice after retries.');
		}
		return result;
	}
}
