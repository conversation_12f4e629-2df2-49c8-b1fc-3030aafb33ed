import { Provider, User } from '@prisma/client';
import { UserService } from '.';

export interface AuthService {
	providerLogin(provider: Provider, provider_id: string): Promise<User>;
	getUserById(userId: string): Promise<User | null>;
	findOrCreateUserByProvider(provider: Provider, providerId: string): Promise<User>;
}

export class AuthServiceImpl implements AuthService {
	constructor(private readonly getUserService: () => UserService) {}

	async providerLogin(provider: Provider, provider_id: string): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, provider_id);
		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id,
			});
		}

		return user;
	}

	async getUserById(userId: string): Promise<User | null> {
		return this.getUserService().getUserById(userId);
	}

	async findOrCreateUserByProvider(provider: Provider, providerId: string): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, providerId);

		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id: providerId,
			});
		}

		return user;
	}
}
