import { Language } from '@prisma/client';
import { aboutTranslations } from './about';
import { accessibilityTranslations } from './accessibility';
import { collectionsTranslations } from './collections';
import { commonTranslations } from './common';
import { difficultyTranslations } from './difficulty';
import { feedbackTranslations } from './feedback';
import { grammarTranslations } from './grammar';
import { headingsTranslations } from './headings';
import { homeTranslations } from './home';
import { languageTranslations } from './language';
import { legalTranslations } from './legal';
import { lengthsTranslations } from './lengths';
import { navTranslations } from './nav';
import { paragraphsTranslations } from './paragraphs';
import { qaPracticeTranslations } from './qa-practice';
import { reviewTranslations } from './review';
import { searchTranslations } from './search';
import { themeTranslations } from './theme';
import { toastTranslations } from './toast';
import { uiTranslations } from './ui';
import { wordsTranslations } from './words';
import { keywordsTranslations } from './keywords';

export * from './about';
export * from './accessibility';
export * from './collections';
export * from './common';
export * from './difficulty';
export * from './errors';
export * from './feedback';
export * from './grammar';
export * from './headings';
export * from './home';
export * from './keywords';
export * from './language';
export * from './legal';
export * from './lengths';
export * from './nav';
export * from './paragraphs';
export * from './qa-practice';
export * from './review';
export * from './search';
export * from './theme';
export * from './ui';
export * from './words';
export * from './toast';

// Define the structure for translation entries
interface TranslationDict {
	[key: string]: Record<Language, string>;
}

// Combine all the smaller translation objects into one
export const translations: TranslationDict = {
	...navTranslations,
	...homeTranslations,
	...feedbackTranslations,
	...aboutTranslations,
	...uiTranslations,
	...collectionsTranslations,
	...themeTranslations,
	...searchTranslations,
	...wordsTranslations,
	...accessibilityTranslations,
	...commonTranslations,
	...reviewTranslations,
	...legalTranslations,
	...languageTranslations,
	...headingsTranslations,
	...lengthsTranslations,
	...paragraphsTranslations,
	...qaPracticeTranslations,
	...toastTranslations,
	...grammarTranslations,
	...difficultyTranslations,
	...keywordsTranslations,
};

export function getTranslationKeyOfLanguage(lang: Language) {
	switch (lang) {
		case 'EN':
			return 'language.EN';
		case 'VI':
			return 'language.VI';
	}
}
