// Export all contexts
export * from './auth-context';
export * from './collections-context';
export * from './keywords-context';
export * from './last-seen-word-context';
export * from './llm-context';
export * from './loading-context';
export * from './media-query-context';
export * from './translation-context';

// Export hooks for backward compatibility (same as original hooks)
export { useAuth } from './auth-context';
export { useCollections, useCollectionsContext } from '../hooks';
export { useWords, useWord, useWordsContext, useCurrentCollectionWords } from '../hooks';
export { useKeywords } from './keywords-context';
export { useLastSeenWord } from './last-seen-word-context';
export { useLLM } from './llm-context';
export { useLoading, useScopedLoading, useLoadingError, LoadingProvider } from './loading-context';
export { useMediaQuery } from './media-query-context';
