'use client';

import { translations } from '@/lib';
import { Language } from '@prisma/client';
import {
	createContext,
	ReactNode,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useScopedLoading } from './loading-context';

// Type for translation keys to ensure type safety
type TranslationKey = keyof typeof translations;

// Enhanced context type with better type safety
interface TranslationContextType {
	language: Language;
	setLanguage: (lang: Language) => void;
	t: (key: TranslationKey, params?: Record<string, string | number>) => string;
	isLoading: boolean;
	getLoadingState: (operation: string) => boolean;
}

// Default language constant
const DEFAULT_LANGUAGE: Language = 'EN';
const STORAGE_KEY = 'language';

// Create context with default values
const TranslationContext = createContext<TranslationContextType>({
	language: DEFAULT_LANGUAGE,
	setLanguage: () => {},
	t: (key: TranslationKey) => String(key),
	isLoading: true,
	getLoadingState: () => false,
});

// Utility function to safely access localStorage
const getStoredLanguage = (): Language | null => {
	if (typeof window === 'undefined') return null;

	try {
		const stored = localStorage.getItem(STORAGE_KEY);
		return stored as Language;
	} catch (error) {
		console.warn('Failed to access localStorage:', error);
		return null;
	}
};

const setStoredLanguage = (language: Language): void => {
	if (typeof window === 'undefined') return;

	try {
		localStorage.setItem(STORAGE_KEY, language);
	} catch (error) {
		console.warn('Failed to save language to localStorage:', error);
	}
};

// Custom hook to use translation with better error handling
export const useTranslation = () => {
	const context = useContext(TranslationContext);

	if (!context) {
		throw new Error('useTranslation must be used within a TranslationProvider');
	}

	return context;
};

// Provider component with enhanced features
export function TranslationProvider({ children }: { children: ReactNode }) {
	const [language, setLanguageState] = useState<Language>(DEFAULT_LANGUAGE);
	const interpolationCache = useRef<Map<string, string>>(new Map());
	const { getLoading } = useScopedLoading('translation');
	const [initLoading, setInitLoading] = useState(true);

	// Compute loading state from specific operations
	const isLoading = initLoading || getLoading('languageChange');

	// Initialize language from localStorage on mount
	useEffect(() => {
		const storedLanguage = getStoredLanguage();
		if (storedLanguage && storedLanguage !== language) {
			setLanguageState(storedLanguage);
		}
		setInitLoading(false);
	}, [language]);

	// Optimized translation function with caching
	const t = useCallback(
		(key: TranslationKey, params?: Record<string, string | number>): string => {
			// Validate translation key exists
			if (!translations[key]) {
				if (process.env.NODE_ENV === 'development') {
					console.warn(`Translation key not found: ${String(key)}`);
				}
				return String(key);
			}

			// Get base text with fallback chain
			const translationObj = translations[key];
			let text = translationObj[language] || translationObj[DEFAULT_LANGUAGE] || String(key);

			// Handle parameter interpolation with caching
			if (params && Object.keys(params).length > 0) {
				const cacheKey = `${String(key)}-${language}-${JSON.stringify(params)}`;

				if (interpolationCache.current.has(cacheKey)) {
					return interpolationCache.current.get(cacheKey)!;
				}

				// More efficient parameter replacement
				for (const [paramKey, paramValue] of Object.entries(params)) {
					text = text.replaceAll(`{${paramKey}}`, String(paramValue));
				}

				// Cache the result
				interpolationCache.current.set(cacheKey, text);

				// Prevent cache from growing too large
				if (interpolationCache.current.size > 1000) {
					interpolationCache.current.clear();
				}
			}

			return text;
		},
		[language]
	);

	// Enhanced language change handler with persistence
	const setLanguage = useCallback(
		(newLanguage: Language) => {
			if (newLanguage === language) return;

			setLanguageState(newLanguage);
			setStoredLanguage(newLanguage);

			// Clear interpolation cache when language changes
			interpolationCache.current.clear();
		},
		[language]
	);

	// Memoize the context value to prevent unnecessary re-renders
	const contextValue = useMemo(
		() => ({
			language,
			setLanguage,
			t,
			isLoading,
			getLoadingState: getLoading,
		}),
		[language, setLanguage, t, isLoading, getLoading]
	);

	return (
		<TranslationContext.Provider value={contextValue}>{children}</TranslationContext.Provider>
	);
}
