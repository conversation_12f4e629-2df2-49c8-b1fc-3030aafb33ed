'use client';

import { useState, useC<PERSON>back, useMemo, createContext, useContext } from 'react';
import {
	create<PERSON><PERSON>word<PERSON><PERSON>,
	delete<PERSON><PERSON>word<PERSON><PERSON>,
	getAll<PERSON>eywordsOfUser<PERSON>pi,
	getK<PERSON>word<PERSON><PERSON>,
	update<PERSON><PERSON>word<PERSON>pi,
} from '@/backend/api';
import { KeywordWithDetail } from '@/models';
import { useLoadingError, useScopedLoading } from './loading-context';

type KeywordsContextType = {
	keywords: KeywordWithDetail[];
	isLoading: boolean;
	error: Error | null;
	fetchKeywords: () => Promise<void>;
	getKeyword: (id: string) => Promise<KeywordWithDetail | null>;
	searchKeywords: (term: string) => Promise<void>;
	createKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	updateKeyword: (id: string, name: string) => Promise<KeywordWithDetail | null>;
	deleteKeyword: (id: string) => Promise<void>;
	getLoadingState: (key: string) => boolean;
};

const KeywordsContext = createContext<KeywordsContextType | undefined>(undefined);

export function KeywordsProvider({ children }: { children: React.ReactNode }) {
	const [keywords, setKeywords] = useState<KeywordWithDetail[]>([]);
	const [error, setError] = useState<Error | null>(null);

	const { getLoading } = useScopedLoading('keywords');
	const loadingErrorHelper = useLoadingError('keywords');

	// Computed loading state from any keywords operation
	const isLoading =
		getLoading('fetchKeywords') ||
		getLoading('getKeyword') ||
		getLoading('searchKeywords') ||
		getLoading('createKeyword') ||
		getLoading('updateKeyword') ||
		getLoading('deleteKeyword');

	const fetchKeywords = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'fetchKeywords');
		start();
		try {
			const result = await getAllKeywordsOfUserApi();
			setKeywords(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to fetch keywords');
			end(error);
		}
	}, [loadingErrorHelper]);

	const getKeyword = useCallback(
		async (id: string): Promise<KeywordWithDetail | null> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getKeyword');
			start();
			try {
				const result = await getKeywordApi(id);
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to fetch keyword');
				end(error);
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const searchKeywords = useCallback(
		async (term: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'searchKeywords');
			start();
			try {
				const result = await getAllKeywordsOfUserApi();
				const filtered = result.filter((keyword) =>
					keyword.content.toLowerCase().includes(term.toLowerCase())
				);
				setKeywords(filtered);
				end();
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to search keywords');
				end(error);
			}
		},
		[loadingErrorHelper]
	);

	const createKeyword = useCallback(
		async (name: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'createKeyword');
			start();
			try {
				const result = await createKeywordApi({ name });
				await fetchKeywords();
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to create keyword');
				end(error);
				return null;
			}
		},
		[fetchKeywords, loadingErrorHelper]
	);

	const updateKeyword = useCallback(
		async (id: string, name: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'updateKeyword');
			start();
			try {
				const result = await updateKeywordApi(id, { name, description: '' });
				await fetchKeywords();
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to update keyword');
				end(error);
				return null;
			}
		},
		[fetchKeywords, loadingErrorHelper]
	);

	const deleteKeyword = useCallback(
		async (id: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'deleteKeyword');
			start();
			try {
				await deleteKeywordApi(id);
				await fetchKeywords();
				end();
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to delete keyword');
				end(error);
			}
		},
		[fetchKeywords, loadingErrorHelper]
	);

	const value = useMemo(
		() => ({
			keywords,
			isLoading,
			error,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoadingState: getLoading,
		}),
		[
			keywords,
			isLoading,
			error,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoading,
		]
	);

	return <KeywordsContext.Provider value={value}>{children}</KeywordsContext.Provider>;
}

export function useKeywordsContext() {
	const context = useContext(KeywordsContext);
	if (context === undefined) {
		throw new Error('useKeywordsContext must be used within a KeywordsProvider');
	}
	return context;
}

// Export hook for backward compatibility
export function useKeywords() {
	return useKeywordsContext();
}
