'use client';

import { createContext, useContext, useState, useCallback, useMemo } from 'react';
import {
	evaluateAnswersApi,
	evaluateTranslation<PERSON>pi,
	generateParagraph<PERSON>pi,
	generateQuestionsApi,
	generateRandomWordsApi,
	generateWordDetailsApi,
	generateGrammarPracticeApi,
} from '@/backend/api';
import {
	AnswerEvaluationResult,
	EvaluateAnswersParams,
	EvaluateTranslationParams,
	GenerateQuestionsParams,
	GrammarPracticeParams,
	GrammarPracticeResultItem,
} from '@/backend/services/llm.service';
import { RandomWord, WordDetail } from '@/models/word';
import { Difficulty, Language } from '@prisma/client';
import { useLoadingError, useScopedLoading } from './loading-context';

type LLMContextType = {
	isLoading: boolean;
	error: Error | null;
	generateRandomTerms: (data: {
		keywords: string[];
		max_terms: number;
		exclude_collection_ids: string[];
		source_language: Language;
		target_language: Language;
	}) => Promise<RandomWord[]>;
	generateWordDetails: (
		terms: string[],
		source_language: Language,
		target_language: Language
	) => Promise<WordDetail[]>;
	generateParagraphs: (data: {
		keywords: string[];
		language: Language;
		difficulty: Difficulty;
		count: number;
		sentenceCount?: number;
	}) => Promise<string[]>;
	evaluateTranslation: (params: EvaluateTranslationParams) => Promise<any>;
	generateQuestions: (params: GenerateQuestionsParams) => Promise<string[]>;
	evaluateAnswers: (params: EvaluateAnswersParams) => Promise<AnswerEvaluationResult[]>;
	generateGrammarPractice: (
		params: GrammarPracticeParams
	) => Promise<GrammarPracticeResultItem[]>;
	clearError: () => void;
	getLoadingState: (key: string) => boolean;
};

const LLMContext = createContext<LLMContextType | undefined>(undefined);

export function LLMProvider({ children }: { children: React.ReactNode }) {
	const [error, setError] = useState<Error | null>(null);

	const { getLoading } = useScopedLoading('llm');
	const loadingErrorHelper = useLoadingError('llm');

	// Computed loading state from any LLM operation
	const isLoading =
		getLoading('generateRandomTerms') ||
		getLoading('generateWordDetails') ||
		getLoading('generateParagraphs') ||
		getLoading('evaluateTranslation') ||
		getLoading('generateQuestions') ||
		getLoading('evaluateAnswers') ||
		getLoading('generateGrammarPractice');

	const generateRandomTerms = useCallback(
		async (data: {
			keywords: string[];
			max_terms: number;
			exclude_collection_ids: string[];
			source_language: Language;
			target_language: Language;
		}) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'generateRandomTerms');
			start();
			try {
				const result = await generateRandomWordsApi(
					data.keywords,
					data.max_terms,
					data.exclude_collection_ids,
					data.source_language,
					data.target_language
				);
				end();
				return result as RandomWord[];
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate random terms');
				end(error);
				return [] as RandomWord[];
			}
		},
		[loadingErrorHelper]
	);

	const generateWordDetails = useCallback(
		async (terms: string[], source_language: Language, target_language: Language) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'generateWordDetails');
			start();
			try {
				const result = await generateWordDetailsApi(
					terms,
					source_language,
					target_language
				);
				end();
				return result as WordDetail[];
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate word details');
				end(error);
				return [] as WordDetail[];
			}
		},
		[loadingErrorHelper]
	);

	const generateParagraphs = useCallback(
		async (data: {
			keywords: string[];
			language: Language;
			difficulty: Difficulty;
			count: number;
			sentenceCount?: number;
		}) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'generateParagraphs');
			start();
			try {
				const result = await generateParagraphApi(
					data.keywords,
					data.language,
					data.difficulty,
					data.count,
					data.sentenceCount
				);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate paragraphs');
				end(error);
				return [] as string[];
			}
		},
		[loadingErrorHelper]
	);

	const evaluateTranslation = useCallback(
		async (params: EvaluateTranslationParams) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'evaluateTranslation');
			start();
			try {
				const result = await evaluateTranslationApi(params);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to evaluate translation');
				end(error);
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const generateQuestions = useCallback(
		async (params: GenerateQuestionsParams) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'generateQuestions');
			start();
			try {
				const result = await generateQuestionsApi(params);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate questions');
				end(error);
				return [] as string[];
			}
		},
		[loadingErrorHelper]
	);

	const evaluateAnswers = useCallback(
		async (params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'evaluateAnswers');
			start();
			try {
				const result = await evaluateAnswersApi(params);
				end();
				return result;
			} catch (err) {
				const error = err instanceof Error ? err : new Error('Failed to evaluate answers');
				end(error);
				return [] as AnswerEvaluationResult[];
			}
		},
		[loadingErrorHelper]
	);

	const generateGrammarPractice = useCallback(
		async (params: GrammarPracticeParams): Promise<GrammarPracticeResultItem[]> => {
			const { start, end } = loadingErrorHelper(
				() => {},
				setError,
				'generateGrammarPractice'
			);
			start();
			try {
				const result = await generateGrammarPracticeApi(params);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to generate grammar practice');
				end(error);
				return [] as GrammarPracticeResultItem[];
			}
		},
		[loadingErrorHelper]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const value = useMemo(
		() => ({
			isLoading,
			error,
			generateRandomTerms,
			generateWordDetails,
			generateParagraphs,
			evaluateTranslation,
			generateQuestions,
			evaluateAnswers,
			generateGrammarPractice,
			clearError,
			getLoadingState: getLoading,
		}),
		[
			isLoading,
			error,
			generateRandomTerms,
			generateWordDetails,
			generateParagraphs,
			evaluateTranslation,
			generateQuestions,
			evaluateAnswers,
			generateGrammarPractice,
			clearError,
			getLoading,
		]
	);

	return <LLMContext.Provider value={value}>{children}</LLMContext.Provider>;
}

export function useLLMContext() {
	const context = useContext(LLMContext);
	if (context === undefined) {
		throw new Error('useLLMContext must be used within a LLMProvider');
	}
	return context;
}

// Export hook for backward compatibility
export function useLLM() {
	return useLLMContext();
}
