'use client';

import {
	getCurrent<PERSON>ser<PERSON><PERSON>,
	getUserByProvider<PERSON>pi,
	logout<PERSON>pi,
	providerLoginApi,
} from '@/backend/api';
import { Provider } from '@prisma/client';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type AuthContextType = {
	user: any | null;
	isLoading: boolean;
	error: Error | null;
	getUser: () => Promise<void>;
	providerLogin: (provider: Provider, provider_id: string) => Promise<void>;
	logout: () => Promise<void>;
	getUserByProvider: (provider: Provider, providerId: string) => Promise<any | null>;
	clearError: () => void;
	getLoadingState: (key: string) => boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
	const [user, setUser] = useState<any | null>(null);
	const [error, setError] = useState<Error | null>(null);

	const { getLoading } = useScopedLoading('auth');
	const loadingErrorHelper = useLoadingError('auth');

	// Computed loading state from any auth operation
	const isLoading = getLoading('getUser') || getLoading('providerLogin') || getLoading('logout') || getLoading('getUserByProvider');

	const getUser = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'getUser');
		start();

		try {
			const result = await getCurrentUserApi();
			setUser(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to get current user');
			end(error);
		}
	}, [loadingErrorHelper]);

	const providerLogin = useCallback(
		async (provider: Provider, provider_id: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'providerLogin');
			start();

			try {
				await providerLoginApi(provider, provider_id);
				await getUser();
				end();
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to login with Telegram');
				end(error);
			}
		},
		[getUser, loadingErrorHelper]
	);

	const logout = useCallback(async () => {
		const { start, end } = loadingErrorHelper(() => {}, setError, 'logout');
		start();

		try {
			await logoutApi();
			setUser(null);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to logout');
			end(error);
		}
	}, [loadingErrorHelper]);

	const getUserByProvider = useCallback(
		async (provider: Provider, providerId: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getUserByProvider');
			start();

			try {
				const result = await getUserByProviderApi(provider, providerId);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to get user by provider');
				end(error);
				return null;
			}
		},
		[loadingErrorHelper]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	const value = useMemo(
		() => ({
			user,
			isLoading,
			error,
			getUser,
			providerLogin,
			logout,
			getUserByProvider,
			clearError,
			getLoadingState: getLoading,
		}),
		[
			user,
			isLoading,
			error,
			getUser,
			providerLogin,
			logout,
			getUserByProvider,
			clearError,
			getLoading,
		]
	);

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuthContext() {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuthContext must be used within an AuthProvider');
	}
	return context;
}

// Export hook for backward compatibility
export function useAuth() {
	return useAuthContext();
}
