'use client';

import { useTranslation } from '@/contexts';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function AboutPage() {
	const { language } = useTranslation();

	return (
		<>
			<div className="mb-6">
				<Link
					href="/"
					className="inline-flex items-center text-muted-foreground hover:text-primary"
				>
					<ArrowLeft className="h-4 w-4 mr-1" /> {/* Adjusted margin for spacing */}
					{language === 'VI' ? 'Quay về trang chủ' : 'Back to Home'}
				</Link>
			</div>

			<h1 className="text-3xl font-bold mb-6">
				{language === 'VI' ? 'Về chúng tôi' : 'About Us'}
			</h1>

			<div className="space-y-6 text-muted-foreground">
				<p>
					{language === 'VI'
						? 'Chào mừng bạn đến với Vocab! Chúng tôi tận tâm giúp bạn xây dựng và mở rộng vốn từ vựng của mình một cách hiệu quả và hấp dẫn.'
						: 'Welcome to Vocab! We are dedicated to helping you build and expand your vocabulary effectively and engagingly.'}
				</p>

				<h2 className="text-xl font-semibold text-foreground mt-8 mb-2">
					{language === 'VI' ? 'Sứ mệnh của chúng tôi' : 'Our Mission'}
				</h2>
				<p>
					{language === 'VI'
						? 'Sứ mệnh của chúng tôi là cung cấp một nền tảng trực quan và thân thiện với người dùng, giúp việc học từ mới trở nên dễ dàng và thú vị. Chúng tôi tin rằng việc có một vốn từ vựng vững chắc là rất quan trọng cho việc giao tiếp hiệu quả và phát triển cá nhân.'
						: 'Our mission is to provide an intuitive and user-friendly platform that makes learning new words easy and enjoyable. We believe that a strong vocabulary is crucial for effective communication and personal growth.'}
				</p>

				<h2 className="text-xl font-semibold text-foreground mt-8 mb-2">
					{language === 'VI' ? 'Cách hoạt động' : 'How It Works'}
				</h2>
				<p>
					{language === 'VI'
						? 'Vocab cho phép bạn tạo các bộ sưu tập từ vựng được cá nhân hóa. Bạn có thể thêm từ, định nghĩa, câu ví dụ và thậm chí cả hình ảnh để giúp bạn ghi nhớ. Ứng dụng sử dụng các thuật toán lặp lại ngắt quãng để tối ưu hóa quá trình học tập của bạn, đảm bảo bạn ôn tập các từ vào những thời điểm hiệu quả nhất.'
						: 'Vocab allows you to create personalized vocabulary collections. You can add words, definitions, example sentences, and even images to help you remember. The application uses spaced repetition algorithms to optimize your learning process, ensuring you review words at the most effective intervals.'}
				</p>

				<h2 className="text-xl font-semibold text-foreground mt-8 mb-2">
					{language === 'VI' ? 'Tính năng' : 'Features'}
				</h2>
				<ul className="list-disc pl-5 space-y-2">
					<li>
						{language === 'VI'
							? 'Tạo và quản lý các bộ sưu tập từ vựng tùy chỉnh.'
							: 'Create and manage custom vocabulary collections.'}
					</li>
					<li>
						{language === 'VI'
							? 'Thêm từ với định nghĩa, câu ví dụ và hình ảnh.'
							: 'Add words with definitions, example sentences, and images.'}
					</li>
					<li>
						{language === 'VI'
							? 'Học tập bằng lặp lại ngắt quãng thông minh.'
							: 'Learn with smart spaced repetition.'}
					</li>
					<li>
						{language === 'VI'
							? 'Theo dõi tiến độ học tập của bạn.'
							: 'Track your learning progress.'}
					</li>
					<li>
						{language === 'VI'
							? 'Giao diện thân thiện và dễ sử dụng.'
							: 'User-friendly and intuitive interface.'}
					</li>
					<li>
						{language === 'VI'
							? 'Truy cập từ vựng của bạn mọi lúc, mọi nơi.'
							: 'Access your vocabulary anytime, anywhere.'}
					</li>
				</ul>

				<h2 className="text-xl font-semibold text-foreground mt-8 mb-2">
					{language === 'VI' ? 'Hướng dẫn sử dụng' : 'How to Use'}
				</h2>
				<div className="rounded-lg border bg-card p-6">
					<h3 className="font-semibold text-foreground mb-3">
						{language === 'VI' ? 'Bắt đầu' : 'Getting Started'}
					</h3>
					<ol className="list-decimal pl-5 space-y-3">
						<li>
							<strong>
								{language === 'VI' ? 'Tạo một bộ sưu tập:' : 'Create a collection:'}
							</strong>{' '}
							{language === 'VI'
								? 'Bắt đầu bằng cách tạo một bộ sưu tập mới cho các từ bạn muốn học. Đặt tên cho nó một cách có ý nghĩa, ví dụ như "Từ vựng IELTS" hoặc "Thuật ngữ kỹ thuật".'
								: 'Start by creating a new collection for the words you want to learn. Give it a meaningful name, e.g., "IELTS Vocabulary" or "Technical Terms".'}
						</li>
						<li>
							<strong>{language === 'VI' ? 'Thêm từ:' : 'Add words:'}</strong>{' '}
							{language === 'VI'
								? 'Thêm các từ vào bộ sưu tập của bạn. Cung cấp định nghĩa, câu ví dụ và bất kỳ ghi chú nào khác có thể giúp bạn hiểu và ghi nhớ từ đó.'
								: 'Add words to your collection. Provide definitions, example sentences, and any other notes that might help you understand and remember the word.'}
						</li>
						<li>
							<strong>
								{language === 'VI' ? 'Học và ôn tập:' : 'Learn and review:'}
							</strong>{' '}
							{language === 'VI'
								? 'Sử dụng tính năng học tập của ứng dụng để ôn tập các từ của bạn. Hệ thống sẽ hiển thị cho bạn các từ vào những khoảng thời gian tối ưu để ghi nhớ lâu dài.'
								: "Use the app's learning feature to review your words. The system will show you words at optimal intervals for long-term retention."}
						</li>
						<li>
							<strong>
								{language === 'VI' ? 'Theo dõi tiến độ:' : 'Track progress:'}
							</strong>{' '}
							{language === 'VI'
								? 'Theo dõi tiến độ học tập của bạn và xem bạn đã thành thạo bao nhiêu từ theo thời gian.'
								: 'Monitor your learning progress and see how many words you have mastered over time.'}
						</li>
					</ol>

					<h3 className="font-semibold text-foreground mt-6 mb-3">
						{language === 'VI' ? 'Mẹo học tập' : 'Learning Tips'}
					</h3>
					<ul className="list-disc pl-5 space-y-2">
						<li>
							{language === 'VI'
								? 'Hãy nhất quán với việc học của bạn. Dành một chút thời gian mỗi ngày để ôn tập từ vựng.'
								: 'Be consistent with your learning. Dedicate some time each day to review vocabulary.'}
						</li>
						<li>
							{language === 'VI'
								? 'Sử dụng các từ mới trong các cuộc trò chuyện và bài viết của bạn để củng cố việc học.'
								: 'Use new words in your conversations and writing to reinforce learning.'}
						</li>
						<li>
							{language === 'VI'
								? 'Tạo mối liên hệ giữa các từ mới và những gì bạn đã biết.'
								: 'Make connections between new words and what you already know.'}
						</li>
						<li>
							{language === 'VI'
								? 'Đừng ngại đoán nghĩa của một từ dựa trên ngữ cảnh trước khi tra cứu.'
								: "Don't be afraid to guess the meaning of a word from context before looking it up."}
						</li>
						<li>
							{language === 'VI'
								? 'Hãy vui vẻ với nó! Học từ vựng có thể là một trải nghiệm bổ ích.'
								: 'Have fun with it! Learning vocabulary can be a rewarding experience.'}
						</li>
					</ul>
				</div>
			</div>
		</>
	);
}
