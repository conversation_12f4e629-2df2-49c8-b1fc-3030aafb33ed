'use client';

import {
	CollectionsSection,
	FeedbackSection,
	GamificationSection,
	HeroSection,
	LearningPathSection,
	ParagraphsSection,
	PracticeTypesSection,
	ReadingWritingSection,
	cardVariants,
	containerVariants,
} from '@/components/home';
import { ScreenReaderAnnouncement } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';

export default function Home() {
	const { t } = useTranslation();

	return (
		<motion.main
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.5 }}
			role="main"
			aria-label="Home page"
		>
			<ScreenReaderAnnouncement message={t('home.title')} priority="polite" />

			<div className="fixed inset-0 -z-10 bg-gradient-to-br from-primary/20 via-background to-secondary/20 animate-gradient-x transition-colors duration-300" />

			<motion.div
				variants={containerVariants}
				initial="hidden"
				animate="visible"
				className="space-y-12 text-center"
			>
				<HeroSection />

				<CollectionsSection />

				<div className="my-8 h-1 w-1/3 mx-auto bg-gradient-to-r from-primary via-secondary to-primary rounded-full opacity-60" />

				<motion.div variants={cardVariants} className="space-y-8">
					<PracticeTypesSection />
				</motion.div>

				<motion.div variants={cardVariants} className="space-y-8">
					<ReadingWritingSection />
				</motion.div>

				<motion.div variants={cardVariants} className="space-y-8">
					<GamificationSection />
				</motion.div>

				<motion.div variants={cardVariants} className="space-y-8">
					<LearningPathSection />
				</motion.div>

				<motion.div variants={cardVariants} className="space-y-8">
					<ParagraphsSection />
				</motion.div>

				<motion.div variants={cardVariants} className="space-y-8">
					<FeedbackSection />
				</motion.div>
			</motion.div>
		</motion.main>
	);
}
