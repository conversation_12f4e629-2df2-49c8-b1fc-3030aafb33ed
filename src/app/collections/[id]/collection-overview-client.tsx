'use client';

import { useCollections } from '@/contexts';
import { useEffect } from 'react';
import { CollectionWithDetail } from '@/models';
import {
	GraduationCap,
	FileText,
	Zap,
	ListChecks,
	RefreshCw,
	Target,
	Edit3,
	MessageSquare,
	CheckCircle2,
} from 'lucide-react';
import { ErrorDisplay } from './components/error-display';
import { CollectionInfoCard } from './components/collection-info-card';
import { FeatureCard } from './components/feature-card';
import { CollectionOverviewSkeleton } from './components/collection-overview-skeleton';

export function CollectionOverviewClient({
	initialCollection,
}: {
	initialCollection?: CollectionWithDetail;
}) {
	const { currentCollection, setCurrentCollection, loading, error, setError } = useCollections();

	useEffect(() => {
		if (initialCollection) setCurrentCollection(initialCollection);
	}, [initialCollection, setCurrentCollection]);

	// Clear errors when component unmounts or collection changes
	useEffect(() => {
		if (error) {
			console.error(error);
			setError(null);
		}
	}, [error, setError]);

	const features = currentCollection
		? [
				{
					titleKey: 'collections.tabs.vocabulary',
					descriptionKey: 'collections.overview.vocabulary_desc',
					icon: GraduationCap,
					link: `/collections/${currentCollection.id}/vocabulary`,
					subFeatures: [
						{
							titleKey: 'collections.tabs.generate_words',
							icon: Zap,
							subLink: '/generate',
						},
						{
							titleKey: 'collections.tabs.my_words_list',
							icon: ListChecks,
							subLink: '/my-words',
						},
						{
							titleKey: 'collections.tabs.review',
							icon: RefreshCw,
							subLink: '/review',
						},
						{
							titleKey: 'collections.tabs.multiple_choice_practice',
							icon: Target,
							subLink: '/mcq',
						},
					],
				},
				{
					titleKey: 'collections.tabs.paragraphs',
					descriptionKey: 'collections.overview.paragraphs_desc',
					icon: FileText,
					link: `/collections/${currentCollection.id}/paragraph`,
					subFeatures: [
						{
							titleKey: 'collections.tabs.paragraph_practice',
							icon: Edit3,
							subLink: '/paragraph-practice',
						},
						{
							titleKey: 'qa_practice.tab_title',
							icon: MessageSquare,
							subLink: '/qa-practice',
						},
						{
							titleKey: 'collections.tabs.grammar_practice',
							icon: CheckCircle2,
							subLink: '/grammar-practice',
						},
					],
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />
			<div className="container mx-auto py-6">
				<CollectionInfoCard collection={currentCollection} />

				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{features.map((feature) => (
						<FeatureCard
							key={feature.titleKey}
							titleKey={feature.titleKey}
							descriptionKey={feature.descriptionKey}
							icon={feature.icon}
							link={feature.link}
							subFeatures={feature.subFeatures}
						/>
					))}
				</div>
			</div>
		</>
	);
}
