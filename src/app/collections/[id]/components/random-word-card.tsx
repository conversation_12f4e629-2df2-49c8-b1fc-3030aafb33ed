'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui';
import { cn } from '@/lib';
import { RandomWord, WordDetail } from '@/models';
import { CheckCircle, Info, PlusCircle } from 'lucide-react'; // Import CheckCircle
import { LoadingSpinner } from '../../../../components/ui/loading-spinner';

interface RandomWordCardProps {
	word: RandomWord;
	detailedWord?: WordDetail;
	onGetDetails?: () => void;
	onAddToCollection?: () => void;
	isDetailLoading?: boolean;
	isAddingToCollection?: boolean;
	isAddedToCollection?: boolean; // Add the new prop
	className?: string;
}

export function RandomWordCard({
	word,
	detailedWord,
	onGetDetails,
	onAddToCollection,
	isDetailLoading = false,
	isAddingToCollection = false,
	isAddedToCollection = false, // Destructure and provide a default value
	className,
}: RandomWordCardProps) {
	const displayData = detailedWord || word;
	const isDetailed = !!detailedWord;

	return (
		<Card
			className={cn(
				'h-full flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200',
				className
			)}
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<span className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm flex-grow">
						{displayData.term}
					</span>
					<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
						{(displayData as RandomWord).partOfSpeech && (
							<span className="text-xs font-semibold px-2 py-1 rounded-md shadow-sm bg-secondary text-secondary-foreground border border-secondary/20">
								{(displayData as RandomWord).partOfSpeech}
							</span>
						)}
					</div>
				</CardTitle>
			</CardHeader>
			<CardContent className="flex-grow flex flex-col p-5">
				{isDetailed ? (
					<div className="space-y-4">
						{detailedWord.definitions && detailedWord.definitions.length > 0 ? (
							detailedWord.definitions.map((def, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									{def.pos && def.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{def.pos.join(', ')}
										</p>
									)}
									{def.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {def.ipa}
										</p>
									)}
									{def.explains && def.explains.length > 0 && (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Explanations:
											</p>
											{def.explains.map((explain, expIndex) => (
												<div
													key={expIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														English:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{explain.EN}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														Vietnamese:
													</p>
													<p className="text-sm text-foreground/95">
														{explain.VI}
													</p>
												</div>
											))}
										</div>
									)}

									{def.examples && def.examples.length > 0 && (
										<div>
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Examples:
											</p>
											{def.examples.map((example, exIndex) => (
												<div
													key={exIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														English:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{example.EN}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														Vietnamese:
													</p>
													<p className="text-sm text-foreground/95">
														{example.VI}
													</p>
												</div>
											))}
										</div>
									)}
								</div>
							))
						) : (
							<p className="p-4 text-sm text-muted-foreground italic">
								No detailed information available.
							</p>
						)}
					</div>
				) : (
					<div className="space-y-4">
						{word.meaning && word.meaning.length > 0 ? (
							word.meaning.map((meaning, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									<p className="text-xs font-medium text-muted-foreground tracking-wide">
										English:
									</p>
									<p className="mb-2 text-sm text-foreground/95">{meaning.EN}</p>
									<p className="text-xs font-medium text-muted-foreground tracking-wide">
										Vietnamese:
									</p>
									<p className="text-sm text-foreground/95">{meaning.VI}</p>
								</div>
							))
						) : (
							<p className="p-4 text-sm text-muted-foreground italic">
								No meaning available.
							</p>
						)}
					</div>
				)}

				<div className="mt-auto flex flex-col space-y-2 pt-5">
					{!isDetailed && onGetDetails && (
						<Button
							onClick={onGetDetails}
							disabled={isDetailLoading}
							variant="outline"
							size="sm"
							className="w-full flex items-center justify-center gap-2 transition-colors duration-150 hover:bg-primary/5 dark:hover:bg-primary/10"
						>
							{isDetailLoading ? (
								<LoadingSpinner size="sm" />
							) : (
								<Info size={16} className="text-primary/80" />
							)}
							<span>{isDetailLoading ? 'Loading Details...' : 'Get Details'}</span>
						</Button>
					)}

					{onAddToCollection &&
						(isAddedToCollection ? (
							// Show 'Added' button when the word is added
							<Button
								disabled={true} // The button is disabled as it's already added
								size="sm"
								variant="secondary" // Use a secondary variant to indicate completed action
								className="w-full flex items-center justify-center gap-2 cursor-not-allowed" // Added cursor style
							>
								<CheckCircle size={16} /> {/* Checkmark icon */}
								<span>Added</span> {/* Text indicating it's added */}
							</Button>
						) : (
							// Show 'Add to Collection' button when not added
							<Button
								onClick={onAddToCollection}
								disabled={isAddingToCollection}
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{isAddingToCollection ? (
									<LoadingSpinner size="sm" />
								) : (
									<PlusCircle size={16} />
								)}
								<span>
									{isAddingToCollection ? 'Adding...' : 'Add to Collection'}
								</span>
							</Button>
						))}
				</div>
			</CardContent>
		</Card>
	);
}
