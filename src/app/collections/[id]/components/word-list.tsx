'use client';

import { <PERSON><PERSON>, <PERSON>, CardContent, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { RandomWord, WordDetail } from '@/models';
import { Loader2, Plus } from 'lucide-react';
import { useCallback } from 'react';

interface WordListProps {
	words: RandomWord[];
	detailedWords: Record<string, WordDetail>;
	onGetDetails: (word: RandomWord) => Promise<void>;
	onAddToCollection: (word: RandomWord) => Promise<void>;
	getLoadingState: (term: string) => { gettingDetail?: boolean; adding?: boolean };
}

export function WordList({
	words,
	detailedWords,
	onGetDetails,
	onAddToCollection,
	getLoadingState,
}: WordListProps) {
	const { t } = useTranslation();

	const handleGetDetails = useCallback(
		async (word: RandomWord) => {
			if (detailedWords[word.term] || getLoadingState(word.term).gettingDetail) return;
			await onGetDetails(word);
		},
		[detailedWords, getLoadingState, onGetDetails]
	);

	const handleAddToCollection = useCallback(
		async (word: RandomWord) => {
			if (getLoadingState(word.term).adding) return;
			await onAddToCollection(word);
		},
		[getLoadingState, onAddToCollection]
	);

	if (words.length === 0) {
		return null;
	}

	return (
		<div className="space-y-4">
			{words.map((word) => {
				const loadingState = getLoadingState(word.term);
				const details = detailedWords[word.term];

				return (
					<Card key={word.term}>
						<CardContent className="p-4">
							<div className="flex items-center justify-between">
								<div className="flex-1">
									<h3 className="text-lg font-semibold">{word.term}</h3>
									{details && (
										<div className="mt-2 text-sm text-muted-foreground">
											<p>{details.definitions[0]?.explains[0]?.EN}</p>
										</div>
									)}
								</div>
								<div className="flex gap-2">
									{!details && !loadingState.gettingDetail && (
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleGetDetails(word)}
										>
											<Translate text="words.get_details" />
										</Button>
									)}
									{loadingState.gettingDetail && (
										<Button variant="outline" size="sm" disabled>
											<Loader2 className="h-4 w-4 animate-spin" />
										</Button>
									)}
									<Button
										variant="default"
										size="sm"
										onClick={() => handleAddToCollection(word)}
										disabled={loadingState.adding}
									>
										{loadingState.adding ? (
											<Loader2 className="h-4 w-4 animate-spin" />
										) : (
											<Plus className="h-4 w-4" />
										)}
									</Button>
								</div>
							</div>
						</CardContent>
					</Card>
				);
			})}
		</div>
	);
}
