'use client';

import { Component, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Translate } from '@/components/ui/translate';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
	onRetry?: () => void;
}

interface State {
	hasError: boolean;
	error?: Error;
}

export class PracticeErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): State {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: any) {
		console.error('Practice Error Boundary caught an error:', error, errorInfo);
	}

	handleRetry = () => {
		this.setState({ hasError: false, error: undefined });
		this.props.onRetry?.();
	};

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				return this.props.fallback;
			}

			return (
				<Card className="max-w-md mx-auto mt-8">
					<CardHeader className="text-center">
						<div className="flex justify-center mb-4">
							<AlertTriangle className="h-12 w-12 text-destructive" />
						</div>
						<CardTitle className="text-destructive">
							<Translate text="errors.practice_error_title" />
						</CardTitle>
					</CardHeader>
					<CardContent className="text-center space-y-4">
						<p className="text-muted-foreground">
							<Translate text="errors.practice_error_desc" />
						</p>
						{this.state.error && (
							<details className="text-left">
								<summary className="cursor-pointer text-sm text-muted-foreground">
									<Translate text="errors.technical_details" />
								</summary>
								<pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
									{this.state.error.message}
								</pre>
							</details>
						)}
						<div className="flex gap-2 justify-center">
							<Button onClick={this.handleRetry} variant="outline">
								<RefreshCw className="h-4 w-4 mr-2" />
								<Translate text="ui.retry" />
							</Button>
							<Button onClick={() => window.location.reload()}>
								<Translate text="ui.reload_page" />
							</Button>
						</div>
					</CardContent>
				</Card>
			);
		}

		return this.props.children;
	}
}

// Hook version for functional components
export function usePracticeErrorHandler() {
	const handleError = (error: Error, context?: string) => {
		console.error(`Practice error${context ? ` in ${context}` : ''}:`, error);
		// You could also send to error reporting service here
	};

	return { handleError };
}
