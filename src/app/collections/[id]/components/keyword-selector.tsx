'use client';

import { Bad<PERSON>, Button, Input, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { Keyword } from '@prisma/client';
import { Plus, X } from 'lucide-react';
import { useCallback, useState } from 'react';

interface KeywordSelectorProps {
	keywords: Keyword[];
	selectedKeywords: string[];
	onKeywordsChange: (keywords: string[]) => void;
	onCreateKeyword: (name: string) => Promise<Keyword | undefined>;
	onDeleteKeyword: (id: string) => Promise<void>;
	getKeywordNameFromId: (id: string) => string;
}

export function KeywordSelector({
	keywords,
	selectedKeywords,
	onKeywordsChange,
	onCreateKeyword,
	onDeleteKeyword,
	getKeywordNameFromId,
}: KeywordSelectorProps) {
	const { t } = useTranslation();
	const [newKeyword, setNewKeyword] = useState('');

	const handleCreateKeyword = useCallback(async () => {
		if (!newKeyword.trim()) return;
		const keyword = await onCreateKeyword(newKeyword.trim());
		if (keyword) {
			setNewKeyword('');
			onKeywordsChange([...selectedKeywords, keyword.id]);
		}
	}, [newKeyword, onCreateKeyword, onKeywordsChange, selectedKeywords]);

	const handleDeleteKeyword = useCallback(
		async (id: string) => {
			await onDeleteKeyword(id);
			onKeywordsChange(selectedKeywords.filter((k) => k !== id));
		},
		[onDeleteKeyword, onKeywordsChange, selectedKeywords]
	);

	const handleToggleKeyword = useCallback(
		(id: string) => {
			if (selectedKeywords.includes(id)) {
				onKeywordsChange(selectedKeywords.filter((k) => k !== id));
			} else {
				onKeywordsChange([...selectedKeywords, id]);
			}
		},
		[selectedKeywords, onKeywordsChange]
	);

	return (
		<div className="space-y-4">
			<div className="flex gap-2">
				<Input
					value={newKeyword}
					onChange={(e) => setNewKeyword(e.target.value)}
					placeholder={t('keywords.enter_new')}
					className="flex-1"
				/>
				<Button
					onClick={handleCreateKeyword}
					disabled={!newKeyword.trim()}
					variant="outline"
				>
					<Plus className="h-4 w-4" />
				</Button>
			</div>

			<div className="flex flex-wrap gap-2">
				{keywords.map((keyword) => (
					<Badge
						key={keyword.id}
						variant={selectedKeywords.includes(keyword.id) ? 'default' : 'outline'}
						className="cursor-pointer"
						onClick={() => handleToggleKeyword(keyword.id)}
					>
						{keyword.content}
						{selectedKeywords.includes(keyword.id) && (
							<button
								onClick={(e) => {
									e.stopPropagation();
									handleDeleteKeyword(keyword.id);
								}}
								className="ml-1 hover:text-destructive"
							>
								<X className="h-3 w-3" />
							</button>
						)}
					</Badge>
				))}
			</div>
		</div>
	);
}
