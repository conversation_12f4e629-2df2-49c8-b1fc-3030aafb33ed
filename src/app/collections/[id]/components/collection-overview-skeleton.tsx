'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';

export function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-6">
			{/* Collection Info Card Skeleton */}
			<Card className="mb-6">
				<CardHeader>
					<div className="h-8 w-64 bg-muted rounded animate-pulse mb-2" />
					<div className="h-4 w-48 bg-muted rounded animate-pulse" />
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
						{[...Array(4)].map((_, i) => (
							<div key={i} className="text-center">
								<div className="h-8 w-16 bg-muted rounded animate-pulse mx-auto mb-2" />
								<div className="h-4 w-20 bg-muted rounded animate-pulse mx-auto" />
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Feature Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{[...Array(2)].map((_, i) => (
					<Card key={i} className="h-64">
						<CardHeader>
							<div className="flex items-center gap-3">
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								<div className="h-6 w-32 bg-muted rounded animate-pulse" />
							</div>
							<div className="h-4 w-48 bg-muted rounded animate-pulse" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[...Array(4)].map((_, j) => (
									<div key={j} className="flex items-center gap-3">
										<div className="h-4 w-4 bg-muted rounded animate-pulse" />
										<div className="h-4 w-40 bg-muted rounded animate-pulse" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
