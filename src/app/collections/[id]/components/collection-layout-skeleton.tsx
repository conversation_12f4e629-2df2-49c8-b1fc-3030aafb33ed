'use client';

export function CollectionLayoutSkeleton() {
	return (
		<div className="w-full sm:py-8">
			{/* Header skeleton */}
			<div className="flex items-center justify-between mb-6">
				<div className="flex items-center gap-2">
					<div className="h-8 w-8 bg-muted rounded animate-pulse" />
					<div className="h-8 w-48 bg-muted rounded animate-pulse" />
					<div className="h-4 w-4 bg-muted rounded animate-pulse" />
				</div>
			</div>

			{/* Content skeleton */}
			<div className="mt-4">
				<div className="h-96 w-full bg-muted rounded animate-pulse" />
			</div>
		</div>
	);
}
