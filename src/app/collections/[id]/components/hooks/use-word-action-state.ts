'use client';

import { useState, useCallback } from 'react';

export type WordActionLoading = Record<
	string,
	{ adding?: boolean; gettingDetail?: boolean; error?: Error | null }
>;

export function useWordActionState() {
	const [wordActionLoading, setWordActionLoading] = useState<WordActionLoading>({});

	const setWordActionState = useCallback(
		(
			term: string,
			state: Partial<{ adding: boolean; gettingDetail: boolean; error: Error | null }>
		) => {
			setWordActionLoading((prev) => ({
				...prev,
				[term]: { ...prev[term], ...state },
			}));
		},
		[]
	);

	const clearWordActionState = useCallback((term: string) => {
		setWordActionLoading((prev) => {
			const newState = { ...prev };
			delete newState[term];
			return newState;
		});
	}, []);

	const getWordActionState = useCallback(
		(term: string) => {
			return wordActionLoading[term] || {};
		},
		[wordActionLoading]
	);

	return {
		wordActionLoading,
		setWordActionState,
		clearWordActionState,
		getWordActionState,
	};
}
