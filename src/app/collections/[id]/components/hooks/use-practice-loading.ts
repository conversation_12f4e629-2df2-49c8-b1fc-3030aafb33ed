'use client';

import { useCollections } from '@/contexts';
import { useMemo } from 'react';

export interface PracticeLoadingState {
	isInitialLoading: boolean;
	isCollectionLoading: boolean;
	isWordsLoading: boolean;
	isSearching: boolean;
	isGenerating: boolean;
	isAnyLoading: boolean;
}

export function usePracticeLoading(additionalLoading?: Record<string, boolean>): PracticeLoadingState {
	const { loading } = useCollections();

	return useMemo(() => {
		const isCollectionLoading = loading.get || loading.setCurrent;
		const isWordsLoading = loading.fetchWords || loading.getWordsToReviewWords;
		const isSearching = loading.wordsSearch;
		const isGenerating = loading.addWords || loading.addTerm;

		// Check additional loading states
		const additionalLoadingValues = additionalLoading ? Object.values(additionalLoading) : [];
		const hasAdditionalLoading = additionalLoadingValues.some(Boolean);

		const isInitialLoading = isCollectionLoading || isWordsLoading;
		const isAnyLoading = isInitialLoading || isSearching || isGenerating || hasAdditionalLoading;

		return {
			isInitialLoading,
			isCollectionLoading,
			isWordsLoading,
			isSearching,
			isGenerating,
			isAnyLoading,
		};
	}, [loading, additionalLoading]);
}

// Specific loading states for different practice types
export function useMcqLoading(additionalLoading?: Record<string, boolean>) {
	const baseLoading = usePracticeLoading(additionalLoading);
	
	return {
		...baseLoading,
		shouldShowSkeleton: baseLoading.isInitialLoading,
		shouldDisableQuiz: baseLoading.isAnyLoading,
	};
}

export function useReviewLoading(additionalLoading?: Record<string, boolean>) {
	const baseLoading = usePracticeLoading(additionalLoading);
	
	return {
		...baseLoading,
		shouldShowSkeleton: baseLoading.isInitialLoading,
		shouldDisableActions: baseLoading.isAnyLoading,
	};
}

export function useParagraphLoading(additionalLoading?: Record<string, boolean>) {
	const baseLoading = usePracticeLoading(additionalLoading);
	
	return {
		...baseLoading,
		shouldShowSkeleton: baseLoading.isInitialLoading,
		shouldDisableGeneration: baseLoading.isAnyLoading,
	};
}
