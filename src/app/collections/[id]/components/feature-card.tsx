'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Translate } from '@/components/ui';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';

interface SubFeature {
	titleKey: string;
	icon: LucideIcon;
	subLink: string;
}

interface FeatureCardProps {
	titleKey: string;
	descriptionKey: string;
	icon: LucideIcon;
	link: string;
	subFeatures?: SubFeature[];
}

export function FeatureCard({
	titleKey,
	descriptionKey,
	icon: Icon,
	link,
	subFeatures,
}: FeatureCardProps) {
	return (
		<Card className="hover:shadow-lg transition-shadow duration-200 flex flex-col">
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="text-xl font-medium">
					<Translate text={titleKey} />
				</CardTitle>
				<Icon className="h-6 w-6 text-muted-foreground" />
			</CardHeader>
			<CardContent className="flex-grow">
				<p className="text-sm text-muted-foreground mb-4">
					<Translate text={descriptionKey} />
				</p>
				{subFeatures && (
					<div className="space-y-2 mb-4">
						{subFeatures.map((sub) => (
							<Link key={sub.titleKey} href={`${link}${sub.subLink}`} passHref>
								<Button
									variant="ghost"
									size="sm"
									className="w-full justify-start text-left"
								>
									<sub.icon className="mr-2 h-4 w-4" />
									<Translate text={sub.titleKey} />
								</Button>
							</Link>
						))}
					</div>
				)}
			</CardContent>
		</Card>
	);
}
