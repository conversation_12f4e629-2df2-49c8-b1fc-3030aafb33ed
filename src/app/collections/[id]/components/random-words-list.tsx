'use client';

import { Loading<PERSON>pinner, Translate } from '@/components/ui';
import { CollectionWithDetail, RandomWord, WordDetail } from '@/models'; // Assuming WordDetail is in @/models
import { Collection } from '@prisma/client';
import { motion } from 'framer-motion';
import { useCallback, useState } from 'react'; // Import useState and useCallback
import { RandomWordCard } from './random-word-card';

interface RandomWordsListProps {
	loading: boolean;
	words: RandomWord[];
	collection: CollectionWithDetail | undefined;
	onGetDetails: (word: RandomWord) => void;
	onAddToCollection: (word: RandomWord, collectionId: string) => Promise<void>;
	detailLoading: Record<string, boolean>;
	addToCollectionLoading: Record<string, boolean>;
	detailedWords: Record<string, WordDetail>; // Add detailedWords prop
}

export function RandomWordsList({
	loading,
	words,
	collection,
	onGetDetails,
	onAddToCollection,
	detailLoading,
	addToCollectionLoading,
	detailedWords, // Destructure detailedWords prop
}: RandomWordsListProps) {
	// State to track which words have been added to the collection
	const [addedWordTerms, setAddedWordTerms] = useState<Set<string>>(new Set());

	// Wrap the onAddToCollection handler to update local state
	const handleAddToCollection = useCallback(
		async (word: RandomWord, collectionId: string) => {
			// Assuming onAddToCollection prop is guaranteed to exist if collection is not null
			await onAddToCollection(word, collectionId);
			// Update state only after successful addition
			setAddedWordTerms((prev) => new Set(prev).add(word.term));
		},
		[onAddToCollection] // Recreate if onAddToCollection changes
	);

	if (loading) {
		return (
			<div className="flex justify-center items-center py-12">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	if (words.length === 0) {
		return (
			<p className="text-center py-8 text-muted-foreground">
				<Translate text="words.no_words_found" />
			</p>
		);
	}

	return (
		<div>
			<h3 className="text-lg font-medium">Random Words List</h3>
			<motion.div className={'mt-4'} initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
				{words.map((word) => (
					<div key={word.term} className="mb-4">
						<RandomWordCard
							word={word}
							detailedWord={detailedWords[word.term]} // Pass detailedWord if available
							onGetDetails={() => onGetDetails(word)}
							onAddToCollection={
								collection
									? () => handleAddToCollection(word, collection.id) // Use the wrapped handler
									: undefined
							}
							isDetailLoading={detailLoading[word.term]}
							isAddingToCollection={addToCollectionLoading[word.term]}
							isAddedToCollection={addedWordTerms.has(word.term)} // Pass the added status
						/>
					</div>
				))}
			</motion.div>
		</div>
	);
}
