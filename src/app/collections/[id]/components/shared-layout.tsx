'use client';

import { KeywordsProvider, LLMProvider } from '@/contexts';
import { ReactNode } from 'react';

interface SharedLayoutProps {
	children: ReactNode;
	providers?: {
		llm?: boolean;
		keywords?: boolean;
	};
}

export function SharedLayout({
	children,
	providers = { llm: true, keywords: true },
}: SharedLayoutProps) {
	const content = <div className="w-full">{children}</div>;

	if (!providers.llm && !providers.keywords) {
		return content;
	}

	let wrappedContent = content;

	if (providers.keywords) {
		wrappedContent = <KeywordsProvider>{wrappedContent}</KeywordsProvider>;
	}

	if (providers.llm) {
		wrappedContent = <LLMProvider>{wrappedContent}</LLMProvider>;
	}

	return wrappedContent;
}
