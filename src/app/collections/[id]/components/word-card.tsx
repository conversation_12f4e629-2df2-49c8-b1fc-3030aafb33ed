'use client';

import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui';
import { cn } from '@/lib';
import { WordDetail } from '@/models';
import { ChevronDown, ChevronUp, PlusCircle, Trash2 } from 'lucide-react';
import { memo, useCallback, useState } from 'react';
import { LoadingSpinner } from '../../../../components/ui/loading-spinner';

interface WordCardProps {
	word: WordDetail;
	onAddToCollection?: () => void;
	isAddingToCollection?: boolean;
	onDeleteWord?: () => void;
	isDeleting?: boolean;
	className?: string;
	isReviewMode?: boolean; // Added for review mode
	showVietnamese?: boolean; // Added to control Vietnamese text visibility in review mode
	onToggleVietnamese?: () => void; // Added to toggle Vietnamese text visibility
	defaultExpanded?: boolean; // Added to control initial expanded state
}

function WordCardComponent({
	word,
	onAddToCollection,
	isAddingToCollection,
	onDeleteWord,
	isDeleting,
	className,
	isReviewMode = false, // Default to false
	showVietnamese = true, // Default to true (relevant only in review mode)
	onToggleVietnamese,
	defaultExpanded, // Accepted defaultExpanded prop
}: WordCardProps) {
	// Use defaultExpanded prop for initial state, fallback to false
	const [isExpanded, setIsExpanded] = useState(defaultExpanded ?? false);

	const handleToggleExpand = useCallback(() => {
		setIsExpanded((prev) => !prev);
	}, []);

	const handleAddToCollection = useCallback(() => {
		if (onAddToCollection) {
			onAddToCollection();
		}
	}, [onAddToCollection]);

	const handleDeleteWord = useCallback(() => {
		if (onDeleteWord) {
			onDeleteWord();
		}
	}, [onDeleteWord]);
	return (
		<Card
			className={cn(
				'h-full flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200',
				className
			)}
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<span className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm flex-grow">
						{word.term}
					</span>
					<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
						<Button
							variant="ghost"
							size="icon"
							onClick={handleToggleExpand}
							aria-label={isExpanded ? 'Collapse' : 'Expand'}
						>
							{isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
						</Button>
					</div>
				</CardTitle>
			</CardHeader>
			{isExpanded && (
				<CardContent className="flex-grow flex flex-col p-5">
					<div className="space-y-4 flex-grow">
						{word.definitions && word.definitions.length > 0 ? (
							word.definitions.map((definition, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									{definition.pos && definition.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{definition.pos.join(', ')}
										</p>
									)}
									{definition.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {definition.ipa}
										</p>
									)}
									{/* Explanations section */}
									{definition.explains && definition.explains.length > 0 ? (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Explanations:
											</p>
											{definition.explains.map((explain, expIndex) => (
												<div
													key={expIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														English:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{explain.EN || (
															<span className="italic opacity-70">
																Explanation not provided
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														Vietnamese:
													</p>
													{(!isReviewMode || showVietnamese) && (
														<p className="text-sm text-foreground/95">
															{explain.VI || (
																<span className="italic opacity-70">
																	Giải thích chưa được cung cấp
																</span>
															)}
														</p>
													)}
													{isReviewMode && !showVietnamese && (
														<p className="text-sm text-muted-foreground italic">
															(Vietnamese hidden)
														</p>
													)}
												</div>
											))}
										</div>
									) : (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Explanations:
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												No explanations provided for this definition.
											</p>
										</div>
									)}

									{/* Examples section */}
									{definition.examples && definition.examples.length > 0 ? (
										<div>
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Examples:
											</p>
											{definition.examples.map((example, exIndex) => (
												<div
													key={exIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														English:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{example.EN || (
															<span className="italic opacity-70">
																Example not provided
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														Vietnamese:
													</p>
													{(!isReviewMode || showVietnamese) && (
														<p className="text-sm text-foreground/95">
															{example.VI || (
																<span className="italic opacity-70">
																	Ví dụ chưa được cung cấp
																</span>
															)}
														</p>
													)}
													{isReviewMode && !showVietnamese && (
														<p className="text-sm text-muted-foreground italic">
															(Vietnamese hidden)
														</p>
													)}
												</div>
											))}
										</div>
									) : (
										<div>
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												Examples:
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												No examples provided for this definition.
											</p>
										</div>
									)}
								</div>
							))
						) : (
							<p className="p-4 text-sm text-muted-foreground italic">
								No definitions available.
							</p>
						)}
					</div>

					{/* Action buttons */}
					<div className="mt-auto flex flex-col space-y-2 pt-5">
						{isReviewMode && onToggleVietnamese && (
							<Button
								onClick={onToggleVietnamese}
								variant="outline"
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{showVietnamese ? (
									<>
										<ChevronUp size={16} /> {/* Or EyeOff */}
										<span>Hide Vietnamese</span>
									</>
								) : (
									<>
										<ChevronDown size={16} /> {/* Or Eye */}
										<span>Show Vietnamese</span>
									</>
								)}
							</Button>
						)}
						{!isReviewMode && onAddToCollection && (
							<Button
								onClick={handleAddToCollection}
								disabled={isAddingToCollection}
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{isAddingToCollection ? (
									<LoadingSpinner size="sm" />
								) : (
									<PlusCircle size={16} />
								)}
								<span>
									{isAddingToCollection ? 'Adding...' : 'Add to Collection'}
								</span>
							</Button>
						)}
						{!isReviewMode && onDeleteWord && (
							<Button
								onClick={handleDeleteWord}
								disabled={isDeleting}
								variant="outline"
								size="sm"
								className="w-full flex items-center justify-center gap-2 transition-colors duration-150 hover:bg-destructive/5 dark:hover:bg-destructive/10 text-destructive hover:text-destructive/90"
							>
								{isDeleting ? <LoadingSpinner size="sm" /> : <Trash2 size={16} />}
								<span>{isDeleting ? 'Deleting...' : 'Delete Word'}</span>
							</Button>
						)}
					</div>
				</CardContent>
			)}{' '}
			{/* Close isExpanded conditional */}
		</Card>
	);
}

const arePropsEqual = (prevProps: WordCardProps, nextProps: WordCardProps) => {
	return (
		prevProps.word.id === nextProps.word.id &&
		prevProps.isAddingToCollection === nextProps.isAddingToCollection &&
		prevProps.isDeleting === nextProps.isDeleting &&
		prevProps.className === nextProps.className &&
		prevProps.isReviewMode === nextProps.isReviewMode && // Added for review mode
		prevProps.showVietnamese === nextProps.showVietnamese && // Added for review mode
		prevProps.defaultExpanded === nextProps.defaultExpanded && // Added for comparison
		// Shallow compare functions assuming they are stable (e.g., from useCallback with stable deps)
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onDeleteWord === nextProps.onDeleteWord &&
		prevProps.onToggleVietnamese === nextProps.onToggleVietnamese // Added for review mode
	);
};

export const WordCard = memo(WordCardComponent, arePropsEqual);
