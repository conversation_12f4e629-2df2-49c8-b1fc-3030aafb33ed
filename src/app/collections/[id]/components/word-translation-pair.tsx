import { Badge } from '@/components/ui';

interface TranslationPairProps {
	en: string;
	vi?: string;
	className?: string;
}

export function TranslationPair({ en, vi, className = '' }: TranslationPairProps) {
	return (
		<div
			className={`bg-card/30 rounded-lg px-3 border border-border py-3 text-sm ${className}`}
		>
			<table className="w-full">
				<tbody>
					<tr>
						<td className="pr-3 align-top">
							<Badge variant="secondary" className="text-muted-foreground">
								English
							</Badge>
						</td>
						<td className="text-muted-foreground m-0 p-0 align-top w-full">{en}</td>
					</tr>
					<tr>
						<td className="pr-3 pt-3 align-top">
							<Badge variant="secondary" className="text-muted-foreground">
								Tiếng Việt
							</Badge>
						</td>
						<td className="text-muted-foreground m-0 pt-3 align-top w-full">{vi}</td>
					</tr>
				</tbody>
			</table>
		</div>
	);
}
