import { motion } from 'framer-motion';
import { TranslationPair } from './word-translation-pair';

interface WordCardDefinitionProps {
	def: any;
	defIndex: number;
}

export function WordCardDefinition({ def, defIndex }: WordCardDefinitionProps) {
	return (
		<motion.div
			initial={{ opacity: 0, x: -20 }}
			animate={{ opacity: 1, x: 0 }}
			transition={{ delay: defIndex * 0.1 }}
			className="space-y-3 pt-4 border-l-2 border-primary/20 pl-4"
		>
			<div className="flex items-center space-x-2 text-sm text-muted-foreground">
				{def.pos && (
					<motion.span
						initial={{ opacity: 0, scale: 0.8 }}
						animate={{ opacity: 1, scale: 1 }}
						className="px-2 py-0.5 rounded-full bg-primary/10 text-xs font-medium"
					>
						{def.pos}
					</motion.span>
				)}
				{def.ipa && <span className="font-mono">{def.ipa}</span>}
			</div>
			<h4 className="text-sm font-medium text-muted-foreground">Definitions</h4>
			{def.explains.map((explain: any, explainIndex: number) => (
				<motion.div
					key={explainIndex}
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: explainIndex * 0.05 }}
				>
					<TranslationPair en={explain.EN} vi={explain.VI} />
				</motion.div>
			))}
			{def.examples && def.examples.length > 0 && (
				<div className="space-y-3">
					<h4 className="text-sm font-medium text-muted-foreground">Examples</h4>
					{def.examples.map((example: any, index: number) => (
						<motion.div
							key={index}
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.05 }}
						>
							<TranslationPair en={example.EN} vi={example.VI} />
						</motion.div>
					))}
				</div>
			)}
		</motion.div>
	);
}
