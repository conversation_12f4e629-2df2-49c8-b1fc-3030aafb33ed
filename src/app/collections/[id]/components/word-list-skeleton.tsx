'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface WordListSkeletonProps {
	count?: number;
}

export function WordListSkeleton({ count = 5 }: WordListSkeletonProps) {
	return (
		<div className="space-y-4">
			{[...Array(count)].map((_, index) => (
				<Card key={index} className="w-full">
					<CardHeader className="pb-3">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-3">
								{/* Audio button skeleton */}
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								{/* Word term skeleton */}
								<div className="h-6 w-32 bg-muted rounded animate-pulse" />
								{/* IPA skeleton */}
								<div className="h-4 w-24 bg-muted rounded animate-pulse" />
							</div>
							{/* Action buttons skeleton */}
							<div className="flex gap-2">
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
							</div>
						</div>
					</CardHeader>
					<CardContent>
						{/* Definition skeleton */}
						<div className="space-y-3">
							<div className="flex items-start gap-2">
								<div className="h-4 w-12 bg-muted rounded animate-pulse" />
								<div className="flex-1">
									<div className="h-4 w-full bg-muted rounded animate-pulse mb-2" />
									<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
								</div>
							</div>
							{/* Example skeleton */}
							<div className="ml-4 space-y-2">
								<div className="h-3 w-full bg-muted rounded animate-pulse" />
								<div className="h-3 w-5/6 bg-muted rounded animate-pulse" />
							</div>
						</div>
					</CardContent>
				</Card>
			))}
		</div>
	);
}
