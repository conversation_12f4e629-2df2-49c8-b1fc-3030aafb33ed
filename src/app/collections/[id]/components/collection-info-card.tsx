'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage } from '@/lib';
import { BookOpen, Calendar, Clock, Globe, Hash } from 'lucide-react';
import { CollectionWithDetail } from '@/models';

interface CollectionInfoCardProps {
	collection: CollectionWithDetail;
}

export function CollectionInfoCard({ collection }: CollectionInfoCardProps) {
	return (
		<Card className="mb-8 bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 border-primary/20">
			<CardHeader>
				<CardTitle className="text-2xl font-semibold flex items-center gap-3">
					<BookOpen className="h-7 w-7 text-primary" />
					<Translate
						text="collections.overview.welcome_title"
						values={{ name: collection.name }}
					/>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
					<div className="flex items-center gap-2">
						<Globe className="h-4 w-4 text-blue-500" />
						<strong>
							<Translate text="languages.source_language" />:
						</strong>{' '}
						<Translate text={getTranslationKeyOfLanguage(collection.source_language)} />
					</div>
					<div className="flex items-center gap-2">
						<Globe className="h-4 w-4 text-green-500" />
						<strong>
							<Translate text="languages.target_language" />:
						</strong>{' '}
						<Translate text={getTranslationKeyOfLanguage(collection.target_language)} />
					</div>
					<div className="flex items-center gap-2">
						<Hash className="h-4 w-4 text-purple-500" />
						<strong>
							<Translate text="collections.overview.word_count" />:
						</strong>{' '}
						{collection.word_ids.length}
					</div>
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-orange-500" />
						<strong>
							<Translate text="collections.overview.created_at" />:
						</strong>{' '}
						{new Date(collection.created_at).toLocaleDateString()}
					</div>
					<div className="flex items-center gap-2">
						<Clock className="h-4 w-4 text-teal-500" />
						<strong>
							<Translate text="collections.overview.updated_at" />:
						</strong>{' '}
						{new Date(collection.updated_at).toLocaleDateString()}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
