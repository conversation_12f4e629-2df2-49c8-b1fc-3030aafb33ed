import { Button, LoadingSpinner } from '@/components/ui';
import { RandomWord } from '@/models';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';
import { TranslationPair } from './word-translation-pair';

interface WordCardMeaningProps {
	word: RandomWord;
	detailLoading: boolean;
	onGetDetails: () => void;
}

export function WordCardMeaning({ word, detailLoading, onGetDetails }: WordCardMeaningProps) {
	return (
		<div className="space-y-3">
			<div className="space-y-3 border-l-2 border-primary/20 pl-4">
				<h4 className="text-sm font-medium text-muted-foreground">Meanings</h4>
				{word.meaning &&
					word.meaning.map((meaning, index) => (
						<motion.div
							key={index}
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.05 }}
						>
							<TranslationPair en={meaning.EN} vi={meaning.VI} />
						</motion.div>
					))}
			</div>
			<div className="flex items-center justify-center py-4">
				<Button
					variant="outline"
					size="sm"
					onClick={onGetDetails}
					disabled={detailLoading}
					className="hover:bg-primary/10"
				>
					{detailLoading ? (
						<LoadingSpinner size="sm" />
					) : (
						<span className="flex items-center">
							<Sparkles className="h-4 w-4 mr-2" />
							Get More Details
						</span>
					)}
				</Button>
			</div>
		</div>
	);
}
