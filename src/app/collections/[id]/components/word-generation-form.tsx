'use client';

import { <PERSON><PERSON>, <PERSON>, LoadingSpinner, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { Keyword } from '@prisma/client';
import { Sparkles, X } from 'lucide-react';
import { useCallback, useState } from 'react';

interface WordGenerationFormProps {
	keywords: Keyword[];
	selectedKeywords: string[];
	onKeywordsChangeAction: (values: string[]) => void;
	onGenerateAction: () => void;
	generatingLoading: boolean;
	onDeleteKeywordAction: (id: string) => Promise<void>;
	onCreateKeywordAction: (name: string) => Promise<Keyword>;
	hideGenerateButton?: boolean;
}

export function WordGenerationForm({
	keywords,
	selectedKeywords,
	onKeywordsChangeAction,
	onGenerateAction,
	generatingLoading,
	onDeleteKeywordAction,
	onCreateKeywordAction,
	hideGenerateButton = false,
}: WordGenerationFormProps) {
	const { t } = useTranslation();
	const [newKeyword, setNewKeyword] = useState('');

	const handleKeywordsChange = useCallback(
		async (values: string[]) => {
			const newKeywords = values.filter((value) => value.startsWith('temp_'));
			let updatedValues = [...values];
			if (newKeywords.length > 0) {
				for (const value of newKeywords) {
					const keywordName = value.split('_').slice(2).join('_');
					try {
						const newKeyword = await onCreateKeywordAction(keywordName);
						updatedValues = updatedValues.map((v) => (v === value ? newKeyword.id : v));
					} catch (error) {
						console.error('Failed to create keyword:', error);
					}
				}
			}
			onKeywordsChangeAction(updatedValues);
		},
		[onCreateKeywordAction, onKeywordsChangeAction]
	);

	const handleAddKeyword = async () => {
		if (!newKeyword.trim()) return;

		const existingKeyword = keywords.find(
			(kw) => kw.content.toLowerCase() === newKeyword.toLowerCase()
		);

		if (existingKeyword) {
			if (!selectedKeywords.includes(existingKeyword.id)) {
				onKeywordsChangeAction([...selectedKeywords, existingKeyword.id]);
			}
		} else {
			try {
				const createdKeyword = await onCreateKeywordAction(newKeyword);
				onKeywordsChangeAction([...selectedKeywords, createdKeyword.id]);
			} catch (error) {
				console.error('Failed to create keyword:', error);
			}
		}
		setNewKeyword('');
	};

	const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === 'Enter') {
			e.preventDefault();
			handleAddKeyword();
		}
	};

	const handleRemoveKeyword = (keywordId: string) => {
		onKeywordsChangeAction(selectedKeywords.filter((id) => id !== keywordId));
	};

	return (
		<Card className="p-6 shadow-lg hover:shadow-xl transition-shadow">
			<div className="flex flex-col space-y-6">
				<div>
					<label className="block text-sm font-medium mb-2">
						<Translate text="words.select_keywords" />
					</label>
					<div className="space-y-4">
						<div className="flex gap-2">
							<input
								type="text"
								value={newKeyword}
								onChange={(e) => setNewKeyword(e.target.value)}
								onKeyPress={handleKeyPress}
								placeholder={t('words.keywords_placeholder')}
								className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50"
							/>
							<Button
								onClick={handleAddKeyword}
								disabled={!newKeyword.trim()}
								className="whitespace-nowrap"
							>
								<Translate text="ui.add" />
							</Button>
						</div>

						<div className="flex flex-wrap gap-2">
							{keywords.map((keyword) => {
								const isSelected = selectedKeywords.includes(keyword.id);
								return (
									<div
										key={keyword.id}
										className={`group flex items-center gap-1 px-3 py-1.5 rounded-full cursor-pointer transition-all duration-200 ${
											isSelected
												? 'bg-primary text-white shadow-md hover:bg-primary/90'
												: 'bg-gray-100 hover:bg-gray-200 text-gray-700'
										}`}
									>
										<span
											onClick={() => {
												if (isSelected) {
													handleRemoveKeyword(keyword.id);
												} else {
													onKeywordsChangeAction([
														...selectedKeywords,
														keyword.id,
													]);
												}
											}}
											className="text-sm font-medium"
										>
											{keyword.content}
										</span>
										<button
											onClick={() => onDeleteKeywordAction(keyword.id)}
											className={`p-0.5 rounded-full transition-colors ${
												isSelected
													? 'text-white/80 hover:text-white hover:bg-white/20'
													: 'text-gray-500 hover:text-gray-700 hover:bg-gray-300'
											}`}
										>
											<X className="h-3.5 w-3.5" />
										</button>
									</div>
								);
							})}
						</div>
					</div>
				</div>

				{!hideGenerateButton && (
					<Button
						className="w-full"
						disabled={generatingLoading || selectedKeywords.length === 0}
						onClick={onGenerateAction}
					>
						{generatingLoading ? (
							<>
								<LoadingSpinner size="sm" />
								<Translate text="ui.loading" />
							</>
						) : (
							<>
								<Sparkles className="h-4 w-4" />
								<Translate text="words.generate_words" />
							</>
						)}
					</Button>
				)}
			</div>
		</Card>
	);
}
