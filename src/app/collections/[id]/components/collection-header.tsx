'use client';

import { Button, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { ArrowLeft, Pencil } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { CollectionWithDetail } from '@/models';

interface CollectionHeaderProps {
	collection: CollectionWithDetail;
	onStartRename: () => void;
	isUpdating?: boolean;
}

export function CollectionHeader({ collection, onStartRename, isUpdating }: CollectionHeaderProps) {
	const router = useRouter();
	const { t } = useTranslation();

	const handleBack = useCallback(() => {
		if (collection) {
			const path = window.location.pathname;
			const expectedPath = `/collections/${collection.id}`;
			if (path === expectedPath) {
				router.push('/collections');
				return;
			}
			router.push('/collections/' + collection.id);
		} else {
			router.push('/collections');
		}
	}, [router, collection]);

	return (
		<div className="flex items-center justify-between mb-6">
			<div className="flex items-center gap-2">
				<Button
					variant="ghost"
					size="sm"
					aria-label={t('ui.back_to_collections_aria')}
					onClick={handleBack}
				>
					<ArrowLeft className="h-4 w-4" />
					<span className="hidden sm:inline">
						<Translate text="ui.back" />
					</span>
				</Button>
				<h1 className="text-2xl font-bold">{collection.name}</h1>
				<button
					type="button"
					className="ml-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
					onClick={onStartRename}
					disabled={isUpdating}
					aria-label={t('collections.rename_collection_aria')}
				>
					<Pencil className="h-4 w-4 hover:text-gray-700 dark:hover:text-gray-300 transition-colors" />
				</button>
			</div>
		</div>
	);
}
