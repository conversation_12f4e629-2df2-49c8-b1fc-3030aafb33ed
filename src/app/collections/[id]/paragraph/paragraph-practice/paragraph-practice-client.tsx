'use client';

import { TranslationEvaluationResult } from '@/backend/services';
import {
	Button,
	DifficultySelector,
	Label,
	Textarea,
	Translate,
	WordGenerationForm,
} from '@/components/ui';
import { useCollections, useLLM, useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { Difficulty, Keyword, Language } from '@prisma/client';
import React, { useCallback, useState } from 'react';
import { useSharedPracticeLogic } from '../../collection-shared-logic';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';

// Constants
const PARAGRAPH_COUNT_FOR_TRANSLATION = 5;

// Types
interface ParagraphPracticeItem {
	originalText: string;
	userTranslation: string;
	evaluationResult?: TranslationEvaluationResult | null;
	isEvaluating: boolean;
}

// Components
const FeedbackSection = ({
	feedback,
	language,
}: {
	feedback: string;
	language: Language;
	collection: any;
	input?: string;
}) => (
	<div>
		<strong className="text-base">
			<Translate text={getTranslationKeyOfLanguage(language)} />:
		</strong>
		<p className="whitespace-pre-wrap text-sm">{feedback}</p>
	</div>
);

const SuggestionsList = ({
	suggestions,
	language,
}: {
	suggestions: string[];
	language: Language;
	collection: any;
	input?: string;
}) => (
	<div className="mt-1">
		<em className="text-sm">
			<Translate text={getTranslationKeyOfLanguage(language)} />:
		</em>
		<ul className="list-disc list-inside ml-4 text-sm">
			{suggestions.map((suggestion, idx) => (
				<li key={`${language}-${idx}`}>{suggestion}</li>
			))}
		</ul>
	</div>
);

const EvaluationResult = ({
	item,
	collection,
}: {
	item: ParagraphPracticeItem;
	collection: any;
	input?: string;
}) => {
	if (item.evaluationResult === null) {
		return (
			<div className="mt-2 p-2 bg-red-100 border-l-4 border-red-500 rounded text-sm text-red-700 dark:bg-red-900/50 dark:border-red-700 dark:text-red-200">
				<Translate text="paragraphs.evaluation_failed_title" />.
			</div>
		);
	}

	if (!item.evaluationResult) return null;

	const getScoreColor = (score: number | null | undefined): 'red' | 'green' | 'yellow' => {
		if (score === undefined || score === null) return 'red';
		return score >= 7 ? 'green' : 'yellow';
	};

	const color = getScoreColor(item.evaluationResult.score);
	const colorClasses = {
		green: 'border-green-500 bg-green-50 dark:border-green-700 dark:bg-green-900/30 text-green-800 dark:text-green-200',
		yellow: 'border-yellow-500 bg-yellow-50 dark:border-yellow-700 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
		red: 'border-red-500 bg-red-50 dark:border-red-700 dark:bg-red-900/30 text-red-800 dark:text-red-200',
	};

	return (
		<div className={`mt-2 p-3 rounded border-l-4 ${colorClasses[color]}`}>
			<div className="flex items-center gap-2">
				<span className="font-semibold">
					<Translate text="paragraphs.feedback_title" />
				</span>
				{item.evaluationResult.score != null && (
					<span className="ml-2 text-sm font-medium">
						<strong>
							<Translate text="paragraphs.score_label" />:
						</strong>{' '}
						{item.evaluationResult.score}/10
					</span>
				)}
			</div>

			<div className="mt-1 space-y-2">
				{item.evaluationResult.feedback?.source_language && (
					<FeedbackSection
						feedback={item.evaluationResult.feedback.source_language}
						language={collection.source_language}
						collection={collection}
					/>
				)}
				{item.evaluationResult.feedback?.target_language && (
					<FeedbackSection
						feedback={item.evaluationResult.feedback.target_language}
						language={collection.target_language}
						collection={collection}
					/>
				)}
			</div>

			{item.evaluationResult.suggestions &&
				(item.evaluationResult.suggestions.source_language?.length > 0 ||
					item.evaluationResult.suggestions.target_language?.length > 0) && (
					<div className="mt-2 pt-2 border-t border-gray-300 dark:border-zinc-700">
						<strong className="text-base">
							<Translate text="paragraphs.suggestions_label" />:
						</strong>
						{item.evaluationResult.suggestions.source_language?.length > 0 && (
							<SuggestionsList
								suggestions={item.evaluationResult.suggestions.source_language}
								language={collection.source_language}
								collection={collection}
							/>
						)}
						{item.evaluationResult.suggestions.target_language?.length > 0 && (
							<SuggestionsList
								suggestions={item.evaluationResult.suggestions.target_language}
								language={collection.target_language}
								collection={collection}
							/>
						)}
					</div>
				)}
		</div>
	);
};

const PracticeItemCard = ({
	item,
	index,
	totalItems,
	collection,
	t,
	onTextAreaChange,
	onEvaluate,
}: {
	item: ParagraphPracticeItem;
	index: number;
	totalItems: number;
	collection: any;
	t: (key: string) => string;
	onTextAreaChange: (index: number, e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	onEvaluate: (index: number) => void;
}) => (
	<div className="p-4 border rounded-lg shadow-lg transition-all bg-card dark:bg-zinc-900 dark:border-zinc-800 space-y-3 relative">
		<div className="flex items-center justify-between mb-2">
			<span className="text-sm font-semibold text-card-foreground dark:text-gray-200">
				<Translate text="paragraphs.original_text_label" /> ({index + 1}/{totalItems})
				<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
					<Translate text={getTranslationKeyOfLanguage(collection.source_language)} />
				</span>
			</span>
		</div>

		<p className="whitespace-pre-wrap bg-muted dark:bg-zinc-800 dark:text-gray-100 p-3 rounded-md text-base">
			{item.originalText}
		</p>

		<div className="mt-3">
			<Label
				htmlFor={`translation-${index}`}
				className="text-sm font-semibold text-card-foreground dark:text-gray-200"
			>
				<Translate text="paragraphs.your_translation_label" />
				<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
					<Translate text={getTranslationKeyOfLanguage(collection.target_language)} />
				</span>
			</Label>

			<Textarea
				id={`translation-${index}`}
				value={item.userTranslation}
				onChange={(e) => onTextAreaChange(index, e)}
				placeholder={t('paragraphs.translation_placeholder')}
				className="mt-1 min-h-[100px]"
				disabled={item.isEvaluating}
			/>

			<div className="mt-3 flex justify-end">
				<Button
					onClick={() => onEvaluate(index)}
					disabled={!item.userTranslation.trim() || item.isEvaluating}
					className="w-full sm:w-auto"
				>
					{item.isEvaluating ? (
						<>
							<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
							<Translate text="paragraphs.evaluating" />
						</>
					) : (
						<Translate text="paragraphs.evaluate" />
					)}
				</Button>
			</div>
		</div>

		{item.evaluationResult && <EvaluationResult item={item} collection={collection} />}
	</div>
);

const PracticeForm = ({
	keywords,
	selectedKeywords,
	onKeywordsChange,
	difficulty,
	onDifficultyChange,
	onGenerate,
	isGenerating,
	onDeleteKeyword,
	onCreateKeyword,
}: {
	keywords: any[];
	selectedKeywords: string[];
	onKeywordsChange: (keywords: string[]) => void;
	difficulty: Difficulty;
	onDifficultyChange: (difficulty: Difficulty) => void;
	onGenerate: () => void;
	isGenerating: boolean;
	onDeleteKeyword: (id: string) => Promise<void>;
	onCreateKeyword: (name: string) => Promise<Keyword | undefined>;
}) => (
	<div className="space-y-6">
		<WordGenerationForm
			keywords={keywords}
			selectedKeywords={selectedKeywords}
			onKeywordsChangeAction={onKeywordsChange}
			onGenerateAction={() => {}}
			generatingLoading={isGenerating}
			onDeleteKeywordAction={onDeleteKeyword}
			onCreateKeywordAction={onCreateKeyword as (name: string) => Promise<Keyword>}
			hideGenerateButton={true}
		/>

		<div className="space-y-4">
			<Label>
				<Translate text="paragraphs.difficulty_label" />
			</Label>
			<DifficultySelector value={difficulty} onChange={onDifficultyChange} />
		</div>

		<Button
			onClick={onGenerate}
			disabled={isGenerating || selectedKeywords.length === 0}
			className="w-full"
		>
			{isGenerating ? (
				<>
					<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
					<Translate text="paragraphs.generating" />
				</>
			) : (
				<Translate text="paragraphs.generate" />
			)}
		</Button>
	</div>
);

export function ParagraphPracticeClient({ params }: { params: { id: string } }) {
	const { t } = useTranslation();
	const { generateParagraphs, evaluateTranslation } = useLLM();
	const [practiceItems, setPracticeItems] = useState<ParagraphPracticeItem[]>([]);
	const { currentCollection, loading } = useCollections();

	const {
		keywords,
		selectedKeywords,
		setSelectedKeywords,
		difficulty,
		setDifficulty,
		handleCreateKeyword,
		handleDeleteKeyword,
		isKeywordsLoading,
		isGeneratingWords,
		setIsGeneratingWords,
	} = useSharedPracticeLogic();

	const handleGenerate = useCallback(async () => {
		if (!currentCollection) return;

		setIsGeneratingWords(true);
		try {
			const paragraphs = await generateParagraphs({
				keywords: selectedKeywords,
				language: currentCollection.source_language,
				difficulty,
				count: PARAGRAPH_COUNT_FOR_TRANSLATION,
			});

			setPracticeItems(
				paragraphs.map((text) => ({
					originalText: text,
					userTranslation: '',
					evaluationResult: null,
					isEvaluating: false,
				}))
			);
		} catch (error) {
			console.error('Error generating paragraphs:', error);
		} finally {
			setIsGeneratingWords(false);
		}
	}, [currentCollection, selectedKeywords, difficulty, setIsGeneratingWords]);

	const handleTextAreaChange = useCallback(
		(index: number, e: React.ChangeEvent<HTMLTextAreaElement>) => {
			setPracticeItems((prev) =>
				prev.map((item, i) =>
					i === index ? { ...item, userTranslation: e.target.value } : item
				)
			);
		},
		[]
	);

	const handleEvaluate = useCallback(
		async (index: number) => {
			if (!currentCollection) return;

			const item = practiceItems[index];
			if (!item.userTranslation.trim()) return;

			setPracticeItems((prev) =>
				prev.map((item, i) => (i === index ? { ...item, isEvaluating: true } : item))
			);

			try {
				const result = await evaluateTranslation({
					original_text: item.originalText,
					translated_text: item.userTranslation,
					source_language: currentCollection.source_language,
					target_language: currentCollection.target_language,
				});

				setPracticeItems((prev) =>
					prev.map((item, i) =>
						i === index
							? { ...item, evaluationResult: result, isEvaluating: false }
							: item
					)
				);
			} catch (error) {
				console.error('Error evaluating translation:', error);
				setPracticeItems((prev) =>
					prev.map((item, i) =>
						i === index
							? { ...item, evaluationResult: null, isEvaluating: false }
							: item
					)
				);
			}
		},
		[currentCollection, practiceItems]
	);

	// Show loading skeleton while collection or keywords are loading
	if (loading.get || loading.setCurrent || isKeywordsLoading) {
		return <PracticeSessionSkeleton type="paragraph" />;
	}

	if (!currentCollection) {
		return (
			<div className="text-center py-8 text-muted-foreground">
				<Translate text="collections.not_found" />
			</div>
		);
	}

	return (
		<div className="space-y-8">
			<PracticeForm
				keywords={keywords}
				selectedKeywords={selectedKeywords}
				onKeywordsChange={setSelectedKeywords}
				difficulty={difficulty}
				onDifficultyChange={setDifficulty}
				onGenerate={handleGenerate}
				isGenerating={isGeneratingWords}
				onDeleteKeyword={handleDeleteKeyword}
				onCreateKeyword={handleCreateKeyword}
			/>

			{practiceItems.length > 0 && (
				<div className="space-y-6">
					{practiceItems.map((item, index) => (
						<PracticeItemCard
							key={index}
							item={item}
							index={index}
							totalItems={practiceItems.length}
							collection={currentCollection}
							t={t}
							onTextAreaChange={handleTextAreaChange}
							onEvaluate={handleEvaluate}
						/>
					))}
				</div>
			)}
		</div>
	);
}
