'use client';

import { GrammarPracticeResultItem } from '@/backend/services';
import {
	Button,
	DifficultySelector,
	Label,
	LoadingSpinner,
	Translate,
	WordGenerationForm,
} from '@/components/ui';
import { useKeywords, useLLM, useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { cn } from '@/lib';
import { Difficulty, Keyword, Language } from '@prisma/client';
import { useCallback, useState, useEffect } from 'react';
import { useSharedPracticeLogic } from '../../collection-shared-logic';

// Constants
const PARAGRAPH_COUNT = 5;

// Types
type SelectedWord = {
	word: string;
	index: number;
};

type ErrorDensity = 'low' | 'medium' | 'high';

type ErrorType = 'grammar' | 'vocabulary' | 'spelling' | 'mechanics';

type ErrorFilter = {
	types: ErrorType[];
	showOnlySelected: boolean;
};

// Components
const InteractiveWord = ({
	word,
	isSelected,
	disabled,
	onClick,
}: {
	word: string;
	index: number;
	isSelected: boolean;
	disabled: boolean;
	onClick: () => void;
}) => (
	<span
		className={cn('cursor-pointer transition-colors duration-150 ease-in-out', {
			'bg-yellow-200 dark:bg-yellow-700 hover:bg-yellow-300 dark:hover:bg-yellow-600 rounded px-0.5 py-0.5':
				isSelected,
			'hover:bg-gray-200 dark:hover:bg-zinc-700 rounded px-0.5 py-0.5':
				!isSelected && !disabled,
			'cursor-not-allowed opacity-70': disabled,
		})}
		onClick={() => !disabled && onClick()}
	>
		{word}
	</span>
);

const GrammarPracticeResult = ({
	item,
	show,
	filteredErrors,
}: {
	item: GrammarPracticeResultItem;
	show: boolean;
	filteredErrors: GrammarPracticeResultItem['allErrors'];
}) =>
	show ? (
		<div className="mt-4 space-y-4">
			<div className="bg-muted/50 p-4 rounded-lg">
				<h4 className="font-medium mb-2">
					<Translate text="grammar.corrections_label" />
				</h4>
				<p className="text-card-foreground dark:text-gray-300">{item.correctedParagraph}</p>
			</div>
			<div className="bg-muted/50 p-4 rounded-lg">
				<h4 className="font-medium mb-2">
					<Translate text="grammar.explanation_label" />
				</h4>
				<ul className="list-disc ml-6">
					{filteredErrors.map((err, idx) => (
						<li key={idx} className="mb-2">
							<span className="font-semibold text-destructive">{err.errorText}</span>{' '}
							→{' '}
							<span className="font-semibold text-success">{err.correctedText}</span>
							<br />
							<span className="text-xs text-muted-foreground">
								{err.errorType}: {err.explanation}
							</span>
						</li>
					))}
				</ul>
			</div>
		</div>
	) : null;

const GrammarPracticeItem = ({
	item,
	index,
	totalItems,
	selectedWords,
	onWordSelection,
	language,
	filteredErrors,
}: {
	item: GrammarPracticeResultItem;
	index: number;
	totalItems: number;
	selectedWords: SelectedWord[];
	onWordSelection: (paragraphIndex: number, word: string, wordIndex: number) => void;
	language: Language;
	filteredErrors: GrammarPracticeResultItem['allErrors'];
}) => {
	const [showResults, setShowResults] = useState(false);
	const words = item.paragraphWithErrors.split(/\s+/);
	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between mb-2">
				<span className="text-sm font-semibold text-card-foreground dark:text-gray-200">
					<Translate text="grammar.original_text_label" /> ({index + 1}/{totalItems}){' '}
					<span className="inline-block bg-primary text-primary-foreground text-xs px-2 py-1 rounded-md font-normal ml-2">
						<Translate text={getTranslationKeyOfLanguage(language)} />
					</span>
				</span>
				<Button variant="outline" size="sm" onClick={() => setShowResults(!showResults)}>
					<Translate
						text={showResults ? 'grammar.hide_results' : 'grammar.show_results'}
					/>
				</Button>
			</div>
			<div className="space-y-2">
				<p className="text-card-foreground dark:text-gray-300 leading-relaxed">
					{words.map((word, wordIndex) => {
						const isSelected = selectedWords.some((s) => s.index === wordIndex);
						const isDisabled = showResults;
						return (
							<InteractiveWord
								key={`${word}-${wordIndex}`}
								word={word}
								index={wordIndex}
								isSelected={isSelected}
								disabled={isDisabled}
								onClick={() => onWordSelection(index, word, wordIndex)}
							/>
						);
					})}
				</p>
				<GrammarPracticeResult
					item={item}
					show={showResults}
					filteredErrors={filteredErrors}
				/>
			</div>
		</div>
	);
};

const ErrorDensitySelector = ({
	value,
	onChange,
}: {
	value: ErrorDensity;
	onChange: (value: ErrorDensity) => void;
}) => (
	<div className="flex gap-2">
		<Button
			variant={value === 'low' ? 'default' : 'outline'}
			size="sm"
			onClick={() => onChange('low')}
		>
			<Translate text="grammar.error_density.low" />
		</Button>
		<Button
			variant={value === 'medium' ? 'default' : 'outline'}
			size="sm"
			onClick={() => onChange('medium')}
		>
			<Translate text="grammar.error_density.medium" />
		</Button>
		<Button
			variant={value === 'high' ? 'default' : 'outline'}
			size="sm"
			onClick={() => onChange('high')}
		>
			<Translate text="grammar.error_density.high" />
		</Button>
	</div>
);

const ErrorTypeFilter = ({
	value,
	onChange,
}: {
	value: ErrorFilter;
	onChange: (filter: ErrorFilter) => void;
}) => (
	<div className="space-y-2">
		<div className="flex flex-wrap gap-2">
			{['grammar', 'vocabulary', 'spelling', 'mechanics'].map((type) => (
				<Button
					key={type}
					variant={value.types.includes(type as ErrorType) ? 'default' : 'outline'}
					size="sm"
					onClick={() => {
						const newTypes = value.types.includes(type as ErrorType)
							? value.types.filter((t) => t !== type)
							: [...value.types, type as ErrorType];
						onChange({ ...value, types: newTypes });
					}}
				>
					<Translate text={`grammar.error_type.${type}`} />
				</Button>
			))}
		</div>
		<div className="flex items-center gap-2">
			<input
				type="checkbox"
				id="show-only-selected"
				checked={value.showOnlySelected}
				onChange={(e) => onChange({ ...value, showOnlySelected: e.target.checked })}
				className="rounded border-gray-300"
			/>
			<Label htmlFor="show-only-selected">
				<Translate text="grammar.show_only_selected" />
			</Label>
		</div>
	</div>
);

const ProgressIndicator = ({
	current,
	total,
	correct,
}: {
	current: number;
	total: number;
	correct: number;
}) => (
	<div className="space-y-2">
		<div className="flex justify-between text-sm">
			<span>
				<Translate text="grammar.progress" />: {current}/{total}
			</span>
			<span>
				<Translate text="grammar.correct_answers" />: {correct}
			</span>
		</div>
		<div className="h-2 bg-gray-200 rounded-full dark:bg-gray-700">
			<div
				className="h-2 bg-primary rounded-full transition-all duration-300"
				style={{ width: `${(current / total) * 100}%` }}
			/>
		</div>
	</div>
);

const useGrammarPractice = (collection: any) => {
	const { generateGrammarPractice, isLoading, error: llmError } = useLLM();
	const [paragraphs, setParagraphs] = useState<GrammarPracticeResultItem[]>([]);
	const [selections, setSelections] = useState<Record<number, SelectedWord[]>>({});
	const [generationState, setGenerationState] = useState<{
		loading: boolean;
		error: Error | null;
	}>({ loading: false, error: null });
	const [keywords, setKeywords] = useState<string[]>([]);
	const [difficulty, setDifficulty] = useState<Difficulty>('BEGINNER');
	const [errorDensity, setErrorDensity] = useState<ErrorDensity>('medium');
	const [errorFilter, setErrorFilter] = useState<ErrorFilter>({
		types: ['grammar', 'vocabulary', 'spelling', 'mechanics'],
		showOnlySelected: false,
	});
	const [progress, setProgress] = useState({ current: 0, total: 0, correct: 0 });

	const generateParagraphs = useCallback(async () => {
		if (!keywords.length) return;
		if (!collection) {
			setGenerationState({ loading: false, error: new Error('Collection not found') });
			return;
		}
		setGenerationState({ loading: true, error: null });
		setParagraphs([]);
		setSelections({});
		setProgress({ current: 0, total: 0, correct: 0 });
		try {
			const result = await generateGrammarPractice({
				keywords,
				language: collection.target_language || 'EN',
				difficulty: difficulty as any,
				count: PARAGRAPH_COUNT,
				errorDensity,
			});
			setParagraphs(result);
			setProgress((prev) => ({ ...prev, total: result.length }));
		} catch (error) {
			const err = error instanceof Error ? error : new Error(String(error));
			setGenerationState({ loading: false, error: err });
		} finally {
			setGenerationState((prev) => ({ ...prev, loading: false }));
		}
	}, [keywords, difficulty, errorDensity, generateGrammarPractice, collection]);

	const toggleWordSelection = useCallback(
		(paragraphIndex: number, word: string, wordIndex: number) => {
			setSelections((prev) => {
				const currentSelections = prev[paragraphIndex] || [];
				const exists = currentSelections.some((s) => s.index === wordIndex);
				const newSelections = exists
					? currentSelections.filter((s) => s.index !== wordIndex)
					: [...currentSelections, { word, index: wordIndex }];

				// Update progress
				const isCorrect = paragraphs[paragraphIndex]?.allErrors.some(
					(err) => err.errorText === word
				);
				if (isCorrect && !exists) {
					setProgress((p) => ({ ...p, correct: p.correct + 1 }));
				} else if (isCorrect && exists) {
					setProgress((p) => ({ ...p, correct: p.correct - 1 }));
				}

				return {
					...prev,
					[paragraphIndex]: newSelections,
				};
			});
		},
		[paragraphs]
	);

	const filteredErrors = useCallback(
		(errors: GrammarPracticeResultItem['allErrors'], paragraphIndex: number) => {
			return errors.filter((error) => {
				const matchesType = errorFilter.types.includes(
					error.errorType.toLowerCase() as ErrorType
				);
				const isSelected = selections[paragraphIndex]?.some(
					(s) => s.word === error.errorText
				);
				return matchesType && (!errorFilter.showOnlySelected || isSelected);
			});
		},
		[errorFilter, selections]
	);

	return {
		paragraphs,
		selections,
		generationState,
		isLoading,
		llmError,
		keywords,
		setKeywords,
		difficulty,
		setDifficulty,
		errorDensity,
		setErrorDensity,
		errorFilter,
		setErrorFilter,
		progress,
		generateParagraphs,
		toggleWordSelection,
		filteredErrors,
	};
};

export function GrammarPracticeClient({ params }: { params: { id: string } }) {
	const { t } = useTranslation();
	const sharedLogic = useSharedPracticeLogic();
	const collection = sharedLogic.currentCollection;
	const { keywords, createKeyword, deleteKeyword } = useKeywords();
	const logic = useGrammarPractice(collection);

	// Update keywords when shared logic changes
	useEffect(() => {
		if (sharedLogic.selectedKeywords.length > 0) {
			logic.setKeywords(sharedLogic.selectedKeywords);
		}
	}, [sharedLogic.selectedKeywords]);

	// Show loading skeleton while collection or keywords are loading
	if (sharedLogic.isKeywordsLoading) {
		return (
			<div className="space-y-6 py-4">
				<header>
					<div className="h-8 w-64 bg-muted rounded animate-pulse mb-2" />
					<div className="h-4 w-96 bg-muted rounded animate-pulse" />
				</header>
				<div className="space-y-4">
					<div className="h-32 w-full bg-muted rounded animate-pulse" />
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<div className="h-20 bg-muted rounded animate-pulse" />
						<div className="h-20 bg-muted rounded animate-pulse" />
					</div>
					<div className="h-10 w-32 bg-muted rounded animate-pulse" />
				</div>
			</div>
		);
	}

	if (!collection) {
		return (
			<div className="text-center py-8 text-muted-foreground">
				<Translate text="collections.not_found" />
			</div>
		);
	}

	return (
		<div className="space-y-6 py-4">
			<header>
				<h1 className="text-2xl font-semibold">
					<Translate text="collections.tabs.grammar_practice" />
				</h1>
				<p className="text-muted-foreground">
					<Translate text="grammar.practice_description" />
				</p>
			</header>
			<section className="space-y-4">
				<WordGenerationForm
					keywords={keywords}
					selectedKeywords={logic.keywords}
					onKeywordsChangeAction={logic.setKeywords}
					onGenerateAction={() => {}}
					generatingLoading={logic.generationState.loading}
					onDeleteKeywordAction={deleteKeyword}
					onCreateKeywordAction={createKeyword as (name: string) => Promise<Keyword>}
					hideGenerateButton
				/>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<div className="space-y-2">
						<Label>
							<Translate text="difficulty.select_difficulty" />
						</Label>
						<DifficultySelector
							value={logic.difficulty}
							onChange={logic.setDifficulty}
							className="w-full"
						/>
					</div>
					<div className="space-y-2">
						<Label>
							<Translate text="grammar.error_density.label" />
						</Label>
						<ErrorDensitySelector
							value={logic.errorDensity}
							onChange={logic.setErrorDensity}
						/>
					</div>
				</div>
				<Button
					onClick={logic.generateParagraphs}
					disabled={logic.generationState.loading || !logic.keywords.length}
					className="w-full sm:w-auto"
				>
					{logic.generationState.loading ? (
						<LoadingSpinner size="sm" />
					) : (
						<Translate text="grammar.view_result_button" />
					)}
				</Button>
			</section>
			{(logic.generationState.error || logic.llmError) && (
				<p className="text-destructive">
					{t('grammar.generation_failed_title')}:{' '}
					{(logic.generationState.error || logic.llmError)?.message}
				</p>
			)}
			{logic.paragraphs.length > 0 && (
				<section className="space-y-8 mt-6">
					<div className="space-y-4">
						<h3 className="text-lg font-semibold">
							<Translate text="grammar.generated_list_title" />
						</h3>
						<ProgressIndicator
							current={logic.progress.current}
							total={logic.progress.total}
							correct={logic.progress.correct}
						/>
						<ErrorTypeFilter
							value={logic.errorFilter}
							onChange={logic.setErrorFilter}
						/>
					</div>
					{logic.paragraphs.map((item, index) => (
						<GrammarPracticeItem
							key={index}
							item={item}
							index={index}
							totalItems={logic.paragraphs.length}
							selectedWords={logic.selections[index] || []}
							onWordSelection={logic.toggleWordSelection}
							language={collection?.target_language || 'EN'}
							filteredErrors={logic.filteredErrors(item.allErrors, index)}
						/>
					))}
				</section>
			)}
		</div>
	);
}
