'use client';

import { useCollections } from '@/contexts';
import { useCallback, useEffect, useState } from 'react';
import { RenameCollectionDialog } from './rename-collection-dialog';
import { CollectionWithDetail } from '@/models';
import { CollectionHeader } from './components/collection-header';
import { ErrorDisplay } from './components/error-display';
import { CollectionLayoutSkeleton } from './components/collection-layout-skeleton';

export function CollectionDetailLayoutClient({
	children,
	initialCollection,
}: {
	children: React.ReactNode;
	initialCollection: CollectionWithDetail;
}) {
	const {
		currentCollection,
		updateCurrentCollection,
		setCurrentCollection,
		loading,
		error,
		setError,
	} = useCollections();

	useEffect(() => {
		setCurrentCollection(initialCollection);
	}, [initialCollection]);

	const [isEditingName, setIsEditingName] = useState(false);

	const renameCollection = useCallback(
		async ({ name }: { name: string }) => {
			await updateCurrentCollection({ name });
		},
		[currentCollection, updateCurrentCollection]
	);

	const handleStartRename = useCallback(() => {
		setIsEditingName(true);
	}, []);

	// Show skeleton while collection is being set
	if (loading.setCurrent && !currentCollection) {
		return <CollectionLayoutSkeleton />;
	}

	if (!currentCollection) {
		return (
			<div className="w-full sm:py-8 text-center">
				<p className="text-muted-foreground">Collection not found</p>
			</div>
		);
	}

	return (
		<div className="w-full sm:py-8">
			<CollectionHeader
				collection={currentCollection}
				onStartRename={handleStartRename}
				isUpdating={loading.update}
			/>

			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			<RenameCollectionDialog
				open={isEditingName}
				onOpenChange={setIsEditingName}
				collection={currentCollection}
				renameCollection={renameCollection}
				renameLoading={loading.update}
				renameError={error}
			/>

			<div className="mt-4">{children}</div>
		</div>
	);
}
