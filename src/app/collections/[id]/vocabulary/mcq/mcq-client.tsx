'use client';

import { Label, RadioGroup, RadioGroupItem } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Translate } from '@/components/ui/translate';
import { useToast } from '@/components/ui/use-toast';
import { useCollections, useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { WordGenerationForm } from '../../components/word-generation-form';
import { useSharedPracticeLogic } from '../../collection-shared-logic';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';

const MIN_WORDS_FOR_QUIZ = 4; // Need at least 1 correct answer and 3 distractors
const NUM_OPTIONS = 4;
const QUIZ_LENGTH = 10; // Number of questions in a quiz session

interface QuizQuestion {
	word: WordDetail; // The word to be guessed (in target_language)
	correctAnswerText: string; // The correct definition (in source_language)
	options: string[]; // Array of 4 definitions, one is correct
	shuffledOptions: { text: string; originalIndex: number }[];
}

// Helper to shuffle an array
function shuffleArray<T>(array: T[]): T[] {
	const newArray = [...array];
	for (let i = newArray.length - 1; i > 0; i--) {
		const j = Math.floor(Math.random() * (i + 1));
		[newArray[i], newArray[j]] = [newArray[j], newArray[i]];
	}
	return newArray;
}

export function McqClient() {
	const { currentCollection, loading, error } = useCollections();
	const collection = currentCollection;
	const isLoading = loading.get || loading.setCurrent;
	const { t } = useTranslation();
	const { toast } = useToast();
	const {
		currentCollectionWords: collectionWords,
		fetchCurrentCollectionWords: fetchWordsByCollection,
		loading: { fetchWords: fetchWordsLoading },
		error: wordsError,
	} = useCollections();

	// Use shared practice logic
	const {
		keywords,
		selectedKeywords,
		setSelectedKeywords,
		handleCreateKeyword,
		handleDeleteKeyword,
		getKeywordNameFromId,
		isKeywordsLoading,
		isGeneratingWords,
		setIsGeneratingWords,
		error: practiceError,
		clearError,
	} = useSharedPracticeLogic();

	const [quizState, setQuizState] = useState<'idle' | 'active' | 'feedback' | 'complete'>('idle');
	const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>([]);
	const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
	const [selectedOption, setSelectedOption] = useState<string | null>(null);
	const [score, setScore] = useState(0);
	const [isAnswerCorrect, setIsAnswerCorrect] = useState<boolean | null>(null);

	useEffect(() => {
		if (collection) {
			fetchWordsByCollection();
		}
	}, [collection, fetchWordsByCollection]);

	const getDefinitionText = useCallback(
		(word: WordDetail, lang: Language): string | undefined => {
			if (word.definitions && word.definitions.length > 0) {
				const def = word.definitions[0];
				if (def.explains && def.explains.length > 0) {
					const explain = def.explains[0];
					return explain[lang];
				}
			}
			return undefined;
		},
		[]
	);

	const generateQuizQuestions = useCallback(() => {
		if (!collection || collectionWords.length < MIN_WORDS_FOR_QUIZ) {
			return [];
		}

		const wordsWithDefinitions = collectionWords.filter(
			(word) => getDefinitionText(word, collection.source_language) !== undefined
		);

		if (wordsWithDefinitions.length < MIN_WORDS_FOR_QUIZ) {
			return [];
		}

		const shuffledWords = shuffleArray(wordsWithDefinitions);
		const questions: QuizQuestion[] = [];
		const usedWordsForQuestions = new Set<string>();

		for (
			let i = 0;
			i < Math.min(shuffledWords.length, QUIZ_LENGTH) && questions.length < QUIZ_LENGTH;
			i++
		) {
			const questionWord = shuffledWords[i];
			if (usedWordsForQuestions.has(questionWord.id)) continue;

			const correctAnswerText = getDefinitionText(questionWord, collection.source_language);
			if (!correctAnswerText) continue; // Should not happen due to filter

			const distractorWords = shuffleArray(
				wordsWithDefinitions.filter((w) => w.id !== questionWord.id)
			);

			const optionsSet = new Set<string>();
			optionsSet.add(correctAnswerText);

			for (const distractor of distractorWords) {
				if (optionsSet.size >= NUM_OPTIONS) break;
				const distractorDef = getDefinitionText(distractor, collection.source_language);
				if (distractorDef && !optionsSet.has(distractorDef)) {
					optionsSet.add(distractorDef);
				}
			}

			// If not enough unique distractors, fill with placeholders (less ideal)
			while (optionsSet.size < NUM_OPTIONS && optionsSet.size < wordsWithDefinitions.length) {
				// This fallback is tricky, ideally we always have enough unique definitions.
				// For simplicity, if this happens, the quiz might have fewer than NUM_OPTIONS or duplicate-looking options (if we add "dummy option X")
				// A better approach for low unique definitions count would be to reduce NUM_OPTIONS for that question or skip the question.
				// For now, we'll proceed, and it might result in fewer than NUM_OPTIONS if not enough unique definitions are found.
				const randomWordForDistractor =
					wordsWithDefinitions[Math.floor(Math.random() * wordsWithDefinitions.length)];
				const randomDef = getDefinitionText(
					randomWordForDistractor,
					collection.source_language
				);
				if (randomDef) optionsSet.add(randomDef);
				else break; // safety break
			}

			if (optionsSet.size > 1) {
				// Need at least one distractor
				const options = Array.from(optionsSet);
				const shuffledOptions = shuffleArray(options).map((opt, idx) => ({
					text: opt,
					originalIndex: options.indexOf(opt), // Not strictly needed but can be useful
				}));

				questions.push({
					word: questionWord,
					correctAnswerText,
					options, // Unshuffled options (correctAnswerText is one of them)
					shuffledOptions,
				});
				usedWordsForQuestions.add(questionWord.id);
			}
		}
		return questions;
	}, [collectionWords, collection, getDefinitionText]);

	const startQuiz = useCallback(() => {
		const newQuestions = generateQuizQuestions();
		if (newQuestions.length === 0) {
			toast({
				variant: 'destructive',
				title: t('collections.mcq.not_enough_words'),
				description: t('collections.mcq.add_more_words'),
			});
			return;
		}
		setQuizQuestions(newQuestions);
		setCurrentQuestionIndex(0);
		setScore(0);
		setSelectedOption(null);
		setIsAnswerCorrect(null);
		setQuizState('active');
	}, [generateQuizQuestions, toast, t]);

	const handleSubmitAnswer = () => {
		if (!selectedOption) {
			toast({
				variant: 'destructive',
				title: t('collections.mcq.select_an_answer'),
			});
			return;
		}

		const currentQuestion = quizQuestions[currentQuestionIndex];
		const correct = selectedOption === currentQuestion.correctAnswerText;
		setIsAnswerCorrect(correct);
		if (correct) {
			setScore((prev) => prev + 1);
		}
		setQuizState('feedback');
	};

	const handleNextQuestion = () => {
		setSelectedOption(null);
		setIsAnswerCorrect(null);
		if (currentQuestionIndex < quizQuestions.length - 1) {
			setCurrentQuestionIndex((prev) => prev + 1);
			setQuizState('active');
		} else {
			setQuizState('complete');
		}
	};

	const currentQuestion = useMemo(() => {
		if (quizState === 'active' || quizState === 'feedback') {
			return quizQuestions[currentQuestionIndex];
		}
		return null;
	}, [quizState, quizQuestions, currentQuestionIndex]);

	// Removed duplicated logic - now using shared practice logic

	const handleGenerateWords = useCallback(async () => {
		if (!collection) {
			toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
			return;
		}
		if (selectedKeywords.length === 0) {
			toast({
				variant: 'destructive',
				title: t('keywords.no_keywords_selected'),
				description: t('keywords.select_at_least_one'),
			});
			return;
		}
		setIsGeneratingWords(true);
		clearError();
		try {
			// After generating words, refresh the collection words and start a new quiz
			await fetchWordsByCollection();
			startQuiz();
		} catch (error: unknown) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.generation_failed'),
				description: err.message,
			});
		} finally {
			setIsGeneratingWords(false);
		}
	}, [
		selectedKeywords,
		fetchWordsByCollection,
		collection,
		toast,
		t,
		startQuiz,
		setIsGeneratingWords,
		clearError,
	]);

	if (isLoading || isKeywordsLoading) {
		return <PracticeSessionSkeleton type="mcq" />;
	}

	if (error) {
		return (
			<div className="text-destructive py-10 text-center">
				<Translate text="errors.general_fetch_error" />: {error.message}
			</div>
		);
	}

	if (!collection) {
		return (
			<div className="py-10 text-center text-muted-foreground">
				<Translate text="collections.not_found" />
			</div>
		);
	}

	if (fetchWordsLoading) {
		return <PracticeSessionSkeleton type="mcq" />;
	}

	if (wordsError) {
		return (
			<div className="text-destructive py-10 text-center">
				<Translate text="errors.general_fetch_error" />: {wordsError.message}
			</div>
		);
	}

	if (collectionWords.length < MIN_WORDS_FOR_QUIZ && quizState === 'idle') {
		return (
			<div className="py-10 text-center">
				<h3 className="text-xl font-semibold mb-2">
					<Translate text="collections.mcq.not_enough_words" />
				</h3>
				<p className="text-muted-foreground">
					<Translate text="collections.mcq.add_more_words" />
				</p>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>
						<Translate text="collections.mcq.title" />
					</CardTitle>
				</CardHeader>
				<CardContent>
					{quizState === 'idle' && (
						<div className="space-y-6">
							<WordGenerationForm
								keywords={keywords || []}
								selectedKeywords={selectedKeywords}
								onKeywordsChange={setSelectedKeywords}
								onCreateKeyword={handleCreateKeyword}
								onDeleteKeyword={handleDeleteKeyword}
								onGenerate={handleGenerateWords}
								isLoading={isGeneratingWords}
								getKeywordNameFromId={getKeywordNameFromId}
							/>
							<Button
								onClick={startQuiz}
								disabled={collectionWords.length < MIN_WORDS_FOR_QUIZ}
								className="w-full"
							>
								<Translate text="collections.mcq.start_quiz" />
							</Button>
						</div>
					)}

					{(quizState === 'active' || quizState === 'feedback') && currentQuestion && (
						<Card className="max-w-2xl mx-auto">
							<CardHeader>
								<CardTitle className="text-center">
									<span className="text-muted-foreground text-sm">
										<Translate
											text="collections.mcq.question_count"
											values={{
												current: currentQuestionIndex + 1,
												total: quizQuestions.length,
											}}
										/>
									</span>
									<p className="mt-2 text-3xl font-semibold">
										{currentQuestion.word.term}
									</p>
									<p className="text-sm font-normal text-muted-foreground">
										(
										<Translate
											text={getTranslationKeyOfLanguage(
												collection.target_language
											)}
										/>
										)
									</p>
								</CardTitle>
							</CardHeader>
							<CardContent>
								<RadioGroup
									value={selectedOption || undefined}
									onValueChange={setSelectedOption}
									disabled={quizState === 'feedback'}
									className="space-y-3"
								>
									{currentQuestion.shuffledOptions.map((opt, index) => (
										<Label
											key={index}
											htmlFor={`option-${index}`}
											className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors
										${
											quizState === 'feedback' &&
											opt.text === currentQuestion.correctAnswerText
												? 'bg-green-100 border-green-500 dark:bg-green-900 dark:border-green-700'
												: quizState === 'feedback' &&
												  opt.text === selectedOption &&
												  !isAnswerCorrect
												? 'bg-red-100 border-red-500 dark:bg-red-900 dark:border-red-700'
												: 'hover:bg-muted/50'
										}
										${selectedOption === opt.text && quizState !== 'feedback' ? 'ring-2 ring-primary' : ''}
									`}
										>
											<RadioGroupItem
												value={opt.text}
												id={`option-${index}`}
												className="mr-3"
											/>
											<span className="flex-1 text-base">{opt.text}</span>
										</Label>
									))}
								</RadioGroup>

								{quizState === 'feedback' && (
									<div
										className={`mt-4 p-3 rounded-md text-sm font-medium
										${
											isAnswerCorrect
												? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200'
												: 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-200'
										}`}
									>
										{isAnswerCorrect ? (
											<Translate text="collections.mcq.correct_answer" />
										) : (
											<>
												<Translate text="collections.mcq.incorrect_answer" />{' '}
												<strong>{currentQuestion.correctAnswerText}</strong>
											</>
										)}
									</div>
								)}

								<div className="mt-6 flex justify-end">
									{quizState === 'active' && (
										<Button
											onClick={handleSubmitAnswer}
											disabled={!selectedOption}
										>
											<Translate text="collections.mcq.submit_answer" />
										</Button>
									)}
									{quizState === 'feedback' && (
										<Button onClick={handleNextQuestion}>
											<Translate text="collections.mcq.next_question" />
										</Button>
									)}
								</div>
							</CardContent>
						</Card>
					)}

					{quizState === 'complete' && (
						<div className="text-center max-w-md mx-auto">
							<h3 className="text-2xl font-semibold mb-4">
								<Translate
									text="collections.mcq.final_score"
									values={{ score, total: quizQuestions.length }}
								/>
							</h3>
							<Button onClick={startQuiz} size="lg">
								<Translate text="ui.play_again" />
							</Button>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
