'use client';

import { useToast } from '@/components/ui';
import { useCollections, useLLM, useTranslation } from '@/contexts';
import { RandomWord, WordDetail } from '@/models';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { WordGenerationForm } from '../../components/word-generation-form';
import { WordList } from '../../components/word-list';
import { useWordActionState } from '../../components/hooks/use-word-action-state';
import { useSharedPracticeLogic } from '../../collection-shared-logic';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';
import { Keyword } from '@prisma/client';

const MAX_TERMS_TO_GENERATE = 10;

export function GenerateWordsClient({ params }: { params: { id: string } }) {
	const { t } = useTranslation();
	const { toast } = useToast();
	const { currentCollection, loading, error } = useCollections();
	const { generateRandomTerms, generateWordDetails, isLoading: isLlmHookLoading } = useLLM();
	const { addTermToCurrentCollection, addWordsToCurrentCollection, refreshCurrentCollection } =
		useCollections();

	// Use shared practice logic
	const {
		keywords,
		selectedKeywords,
		setSelectedKeywords,
		handleCreateKeyword,
		handleDeleteKeyword,
		getKeywordNameFromId,
		isKeywordsLoading,
		isGeneratingWords,
		setIsGeneratingWords,
	} = useSharedPracticeLogic();

	const collection = currentCollection;
	const isLoading = loading.get || loading.setCurrent;

	// State
	const [randomWords, setRandomWords] = useState<RandomWord[]>([]);
	const [detailedWords, setDetailedWords] = useState<Record<string, WordDetail>>({});
	const { wordActionLoading, setWordActionState, clearWordActionState, getWordActionState } =
		useWordActionState();

	const keywordsData = collection ? keywords || [] : [];

	// Handlers - removed duplicated logic, now using shared practice logic

	const handleGenerateWords = useCallback(async () => {
		if (!collection) {
			toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
			return;
		}
		if (selectedKeywords.length === 0) {
			toast({
				variant: 'destructive',
				title: t('keywords.no_keywords_selected'),
				description: t('keywords.select_at_least_one'),
			});
			return;
		}
		setIsGeneratingWords(true);
		try {
			const keywordNames = selectedKeywords.map(getKeywordNameFromId);
			const generatedTerms = await generateRandomTerms({
				keywords: keywordNames,
				max_terms: MAX_TERMS_TO_GENERATE,
				exclude_collection_ids: [collection.id],
				source_language: collection.source_language,
				target_language: collection.target_language,
			});
			setRandomWords(generatedTerms);
		} catch (error: unknown) {
			const err = error instanceof Error ? error : new Error(String(error));
			toast({
				variant: 'destructive',
				title: t('words.generation_failed'),
				description: err.message,
			});
		} finally {
			setIsGeneratingWords(false);
		}
	}, [
		selectedKeywords,
		getKeywordNameFromId,
		generateRandomTerms,
		collection,
		toast,
		t,
		setIsGeneratingWords,
	]);

	const handleGetDetails = useCallback(
		async (word: RandomWord) => {
			if (!collection) {
				toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
				return;
			}
			if (getWordActionState(word.term).gettingDetail || detailedWords[word.term]) return;

			setWordActionState(word.term, { gettingDetail: true, error: null });

			try {
				const detailsList = await generateWordDetails(
					[word.term],
					collection.source_language,
					collection.target_language
				);
				if (detailsList && detailsList.length > 0) {
					setDetailedWords((prev) => ({
						...prev,
						[word.term]: detailsList[0] as WordDetail,
					}));
				} else {
					throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
				}
			} catch (error: unknown) {
				const err = error instanceof Error ? error : new Error(String(error));
				setWordActionState(word.term, { gettingDetail: false, error: err });
				toast({
					variant: 'destructive',
					title: t('words.detail_fetch_error'),
					description: err.message,
				});
			} finally {
				setWordActionState(word.term, { gettingDetail: false });
			}
		},
		[
			generateWordDetails,
			collection,
			toast,
			t,
			getWordActionState,
			detailedWords,
			setWordActionState,
		]
	);

	const handleAddToCollection = useCallback(
		async (word: RandomWord) => {
			if (!collection) {
				toast({ variant: 'destructive', title: t('collections.not_loaded_error') });
				return;
			}
			if (getWordActionState(word.term).adding) return;

			setWordActionState(word.term, { adding: true, error: null });

			try {
				if (detailedWords[word.term]?.id) {
					await addWordsToCurrentCollection([detailedWords[word.term].id]);
				} else {
					await addTermToCurrentCollection(word.term, collection.target_language);
				}
				toast({
					title: t('words.word_added'),
					description: t('words.word_added_desc', { term: word.term }),
				});
				await refreshCurrentCollection();

				setRandomWords((prev) => prev.filter((rw) => rw.term !== word.term));
				setDetailedWords((prev) => {
					const newDetails = { ...prev };
					delete newDetails[word.term];
					return newDetails;
				});
				clearWordActionState(word.term);
			} catch (error: unknown) {
				const err = error instanceof Error ? error : new Error(String(error));
				setWordActionState(word.term, { adding: false, error: err });
				toast({
					variant: 'destructive',
					title: t('words.add_error'),
					description: t('words.add_error_desc', {
						term: word.term,
						message: err.message,
					}),
				});
			} finally {
				setWordActionState(word.term, { adding: false });
			}
		},
		[
			addWordsToCurrentCollection,
			addTermToCurrentCollection,
			collection,
			detailedWords,
			getWordActionState,
			refreshCurrentCollection,
			setWordActionState,
			clearWordActionState,
			toast,
			t,
		]
	);

	if (isLoading || isKeywordsLoading) {
		return <PracticeSessionSkeleton type="paragraph" />;
	}

	return (
		<div className="space-y-6">
			<WordGenerationForm
				keywords={keywordsData}
				selectedKeywords={selectedKeywords}
				onKeywordsChangeAction={setSelectedKeywords}
				onCreateKeywordAction={handleCreateKeyword as (name: string) => Promise<Keyword>}
				onDeleteKeywordAction={handleDeleteKeyword}
				onGenerateAction={handleGenerateWords}
				generatingLoading={isGeneratingWords}
			/>

			<WordList
				words={randomWords}
				detailedWords={detailedWords}
				onGetDetails={handleGetDetails}
				onAddToCollection={handleAddToCollection}
				getLoadingState={getWordActionState}
			/>
		</div>
	);
}
