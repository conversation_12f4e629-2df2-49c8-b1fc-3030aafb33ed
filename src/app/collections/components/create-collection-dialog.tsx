'use client';

import {
	Input,
	Label,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Translate,
	Dialog,
	DialogContent,
	DialogHeader,
	<PERSON>alogTitle,
	DialogFooter,
} from '@/components/ui';
import { Language } from '@prisma/client';
import { Check, X } from 'lucide-react';
import React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

interface LanguageOption {
	value: Language;
	label: string;
}

export function CreateCollectionDialog({
	isOpen,
	onOpenChange,
	onSubmit,
	languageOptions,
	inputClass,
	t,
}: {
	isOpen: boolean;
	onOpenChange: (isOpen: boolean) => void;
	onSubmit: (name: string, targetLanguage: Language, sourceLanguage: Language) => Promise<void>;
	languageOptions: LanguageOption[];
	inputClass: string;
	t: (key: string) => string;
}) {
	const [formState, setFormState] = useState<{
		name: string;
		source_language: Language;
		target_language: Language;
	}>({
		name: '',
		source_language: Language.VI,
		target_language: Language.EN,
	});

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!formState.name.trim()) return;
		try {
			await onSubmit(formState.name, formState.target_language, formState.source_language);
			// Reset form and close dialog is handled by the parent component after successful submission
			// to ensure the main collections list is updated before closing.
			// For now, just reset local form state
			setFormState({
				name: '',
				target_language: Language.EN,
				source_language: Language.VI,
			});
		} catch (error) {
			console.error('Failed to create collection:', error);
			// Optionally, display an error message within the dialog
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent>
				<form onSubmit={handleSubmit}>
					<DialogHeader>
						<DialogTitle>
							<Translate text="collections.create" />
						</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="create-collection-name">
								<Translate text="collections.name" />
							</Label>
							<Input
								id="create-collection-name"
								value={formState.name}
								onChange={(e) =>
									setFormState((prev) => ({
										...prev,
										name: e.target.value,
									}))
								}
								placeholder={t('collections.name_placeholder')}
								className={inputClass}
							/>
						</div>
						<div className="flex space-x-8">
							{/* Source Language Select */}
							<div className="space-y-2 flex-1">
								<Label htmlFor="create-source-language">
									<Translate text="collections.source_language" />
								</Label>
								<Select
									value={formState.source_language}
									onValueChange={(value: Language) =>
										setFormState((prev) => ({
											...prev,
											source_language: value,
										}))
									}
								>
									<SelectTrigger className={inputClass}>
										<SelectValue
											placeholder={t('collections.select_source_language')}
										/>
									</SelectTrigger>
									<SelectContent>
										{languageOptions.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												<Translate text={option.label} />
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							{/* Target Language Select */}
							<div className="space-y-2 flex-1">
								<Label htmlFor="create-target-language">
									<Translate text="collections.target_language" />
								</Label>
								<Select
									value={formState.target_language}
									onValueChange={(value: Language) =>
										setFormState((prev) => ({
											...prev,
											target_language: value,
										}))
									}
								>
									<SelectTrigger className={inputClass}>
										<SelectValue
											placeholder={t('collections.select_target_language')}
										/>
									</SelectTrigger>
									<SelectContent>
										{languageOptions.map((option) => (
											<SelectItem key={option.value} value={option.value}>
												<Translate text={option.label} />
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
						</div>
					</div>
					<DialogFooter className="mt-6">
						<Button
							type="button"
							variant="outline"
							onClick={() => onOpenChange(false)}
							className="gap-2"
						>
							<X size={16} />
							<Translate text="ui.cancel" />
						</Button>
						<Button type="submit" disabled={!formState.name.trim()} className="gap-2">
							<Check size={16} />
							<Translate text="ui.create" />
						</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
