/**
 * Text diffing utilities for highlighting differences between original and corrected text
 */

export interface DiffResult {
  type: 'equal' | 'delete' | 'insert';
  text: string;
}

export interface HighlightedText {
  original: string;
  corrected: string;
}

/**
 * Simple word-based diff algorithm
 * Compares two texts word by word and identifies differences
 */
export function diffTexts(original: string, corrected: string): DiffResult[] {
  const originalWords = original.split(/\s+/);
  const correctedWords = corrected.split(/\s+/);
  
  const result: DiffResult[] = [];
  let i = 0, j = 0;
  
  while (i < originalWords.length || j < correctedWords.length) {
    if (i >= originalWords.length) {
      // Remaining words in corrected are insertions
      result.push({ type: 'insert', text: correctedWords[j] });
      j++;
    } else if (j >= correctedWords.length) {
      // Remaining words in original are deletions
      result.push({ type: 'delete', text: originalWords[i] });
      i++;
    } else if (originalWords[i] === correctedWords[j]) {
      // Words are the same
      result.push({ type: 'equal', text: originalWords[i] });
      i++;
      j++;
    } else {
      // Words are different - look ahead to find best match
      let foundMatch = false;
      
      // Look for the corrected word in upcoming original words
      for (let k = i + 1; k < Math.min(i + 5, originalWords.length); k++) {
        if (originalWords[k] === correctedWords[j]) {
          // Found a match - mark intermediate words as deletions
          for (let l = i; l < k; l++) {
            result.push({ type: 'delete', text: originalWords[l] });
          }
          result.push({ type: 'equal', text: originalWords[k] });
          i = k + 1;
          j++;
          foundMatch = true;
          break;
        }
      }
      
      if (!foundMatch) {
        // Look for the original word in upcoming corrected words
        for (let k = j + 1; k < Math.min(j + 5, correctedWords.length); k++) {
          if (correctedWords[k] === originalWords[i]) {
            // Found a match - mark intermediate words as insertions
            for (let l = j; l < k; l++) {
              result.push({ type: 'insert', text: correctedWords[l] });
            }
            result.push({ type: 'equal', text: correctedWords[k] });
            j = k + 1;
            i++;
            foundMatch = true;
            break;
          }
        }
      }
      
      if (!foundMatch) {
        // No match found - treat as substitution
        result.push({ type: 'delete', text: originalWords[i] });
        result.push({ type: 'insert', text: correctedWords[j] });
        i++;
        j++;
      }
    }
  }
  
  return result;
}

/**
 * Generate highlighted HTML for text differences
 */
export function highlightTextDifferences(original: string, corrected: string): HighlightedText {
  if (!original || !corrected) {
    return { original: original || '', corrected: corrected || '' };
  }
  
  const diffs = diffTexts(original, corrected);
  
  let highlightedOriginal = '';
  let highlightedCorrected = '';
  
  for (const diff of diffs) {
    switch (diff.type) {
      case 'equal':
        highlightedOriginal += diff.text + ' ';
        highlightedCorrected += diff.text + ' ';
        break;
      case 'delete':
        highlightedOriginal += `<span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-1 rounded">${diff.text}</span> `;
        break;
      case 'insert':
        highlightedCorrected += `<span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-1 rounded">${diff.text}</span> `;
        break;
    }
  }
  
  return {
    original: highlightedOriginal.trim(),
    corrected: highlightedCorrected.trim()
  };
}

/**
 * Alternative highlighting based on error information
 * This can be used when we have specific error details from the AI
 */
export function highlightErrorsInText(
  text: string,
  errors: Array<{ errorText: string; correctedText: string }>,
  isOriginal: boolean = true
): string {
  if (!text || !errors || errors.length === 0) {
    return text;
  }
  
  let highlightedText = text;
  
  // Sort errors by length (longest first) to avoid partial replacements
  const sortedErrors = [...errors].sort((a, b) => b.errorText.length - a.errorText.length);
  
  for (const error of sortedErrors) {
    const targetText = isOriginal ? error.errorText : error.correctedText;
    const highlightClass = isOriginal 
      ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-1 rounded'
      : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-1 rounded';
    
    // Use word boundaries to avoid partial matches
    const regex = new RegExp(`\\b${targetText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
    highlightedText = highlightedText.replace(
      regex,
      `<span class="${highlightClass}">${targetText}</span>`
    );
  }
  
  return highlightedText;
}